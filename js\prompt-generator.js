// ===== محرك توليد البرومبت الاحترافي =====

class PromptGenerator {
    constructor() {
        this.currentOptions = { ...CONFIG.DEFAULT_PROMPT_OPTIONS };
        this.generatedPrompt = '';
        this.isGenerating = false;
        this.isPaused = false;
        this.currentStep = 0;
        this.totalSteps = 0;
    }

    // تحديث خيارات البرومبت
    updateOptions(options) {
        this.currentOptions = { ...this.currentOptions, ...options };
    }

    // الحصول على الخيارات المفعلة فقط
    getEnabledOptions() {
        const enabled = {};
        Object.keys(this.currentOptions).forEach(key => {
            if (this.currentOptions[key].enabled) {
                enabled[key] = this.currentOptions[key];
            }
        });
        return enabled;
    }

    // بناء البرومبت الأساسي
    buildBasePrompt(userInput) {
        const enabledOptions = this.getEnabledOptions();
        let prompt = `قم بإنشاء برومبت احترافي ومتكامل للمشروع التالي:\n\n`;
        prompt += `الوصف الأساسي: ${userInput}\n\n`;
        prompt += `يجب أن يتضمن البرومبت العناصر التالية:\n\n`;

        Object.keys(enabledOptions).forEach(key => {
            const option = enabledOptions[key];
            prompt += `${option.title}:\n`;
            prompt += `${option.defaultValue}\n\n`;
        });

        prompt += `\nمتطلبات إضافية:\n`;
        prompt += `- يجب أن يكون البرومبت مفصلاً ومحدداً\n`;
        prompt += `- استخدم اللغة العربية الفصحى\n`;
        prompt += `- اجعل البرومبت قابلاً للتطبيق العملي\n`;
        prompt += `- تأكد من شمولية جميع الجوانب المطلوبة\n`;
        prompt += `- لا تضع مقدمات أو خاتمات، فقط المحتوى المطلوب\n\n`;

        return prompt;
    }

    // توليد برومبت محسن باستخدام AI
    async generateEnhancedPrompt(userInput, progressCallback) {
        this.isGenerating = true;
        this.isPaused = false;
        this.currentStep = 0;
        this.totalSteps = 5;

        try {
            // الخطوة 1: تحليل المدخلات
            await this.updateProgress(1, 'تحليل المدخلات الأساسية...', progressCallback);
            const analysisPrompt = this.buildAnalysisPrompt(userInput);
            const analysis = await apiManager.sendRequest(analysisPrompt);

            if (this.isPaused) return null;

            // الخطوة 2: بناء البرومبت الأساسي
            await this.updateProgress(2, 'بناء البرومبت الأساسي...', progressCallback);
            const basePrompt = this.buildBasePrompt(userInput);

            if (this.isPaused) return null;

            // الخطوة 3: تحسين البرومبت
            await this.updateProgress(3, 'تحسين وتطوير البرومبت...', progressCallback);
            const enhancementPrompt = this.buildEnhancementPrompt(basePrompt, analysis);
            const enhancedPrompt = await apiManager.sendRequest(enhancementPrompt);

            if (this.isPaused) return null;

            // الخطوة 4: إضافة التفاصيل المتقدمة
            await this.updateProgress(4, 'إضافة التفاصيل المتقدمة...', progressCallback);
            const detailsPrompt = this.buildDetailsPrompt(enhancedPrompt);
            const detailedPrompt = await apiManager.sendRequest(detailsPrompt);

            if (this.isPaused) return null;

            // الخطوة 5: المراجعة النهائية والتنسيق
            await this.updateProgress(5, 'المراجعة النهائية والتنسيق...', progressCallback);
            const finalPrompt = this.buildFinalPrompt(detailedPrompt);
            const result = await apiManager.sendRequest(finalPrompt);

            this.generatedPrompt = this.formatFinalResult(result);
            
            await this.updateProgress(5, 'تم الانتهاء بنجاح!', progressCallback);
            
            return this.generatedPrompt;

        } catch (error) {
            console.error('خطأ في توليد البرومبت:', error);
            throw new Error(`فشل في توليد البرومبت: ${error.message}`);
        } finally {
            this.isGenerating = false;
        }
    }

    // بناء برومبت التحليل
    buildAnalysisPrompt(userInput) {
        return `قم بتحليل المشروع التالي وحدد:
1. المجال الأساسي
2. الأهداف المحتملة
3. الجمهور المستهدف المتوقع
4. التحديات المحتملة
5. المتطلبات التقنية

وصف المشروع: ${userInput}

قدم تحليلاً مختصراً ومفيداً في نقاط واضحة.`;
    }

    // بناء برومبت التحسين
    buildEnhancementPrompt(basePrompt, analysis) {
        return `بناءً على التحليل التالي:
${analysis}

قم بتحسين البرومبت التالي ليكون أكثر دقة وتخصصاً:
${basePrompt}

تأكد من:
- دقة المصطلحات التقنية
- وضوح المتطلبات
- شمولية الجوانب المهمة
- قابلية التطبيق العملي`;
    }

    // بناء برومبت التفاصيل
    buildDetailsPrompt(enhancedPrompt) {
        return `قم بإضافة تفاصيل متقدمة للبرومبت التالي:
${enhancedPrompt}

أضف:
- خطوات تنفيذ مفصلة
- معايير الجودة والتقييم
- أمثلة عملية حيثما أمكن
- نصائح للتنفيذ الأمثل
- تحديد المخاطر وطرق تجنبها`;
    }

    // بناء البرومبت النهائي
    buildFinalPrompt(detailedPrompt) {
        return `قم بمراجعة وتنسيق البرومبت التالي ليكون في أفضل صورة احترافية:
${detailedPrompt}

تأكد من:
- التنسيق الواضح والمنظم
- استخدام الرموز والأيقونات المناسبة
- ترتيب المعلومات بشكل منطقي
- وضوح اللغة وسلاسة القراءة
- إزالة أي تكرار أو معلومات غير ضرورية

اجعل البرومبت جاهزاً للاستخدام المباشر.`;
    }

    // تنسيق النتيجة النهائية
    formatFinalResult(result) {
        // إزالة المقدمات والخاتمات غير المرغوبة
        let formatted = result.replace(/^.*?(?=🧠|الهدف|المجال)/s, '');
        formatted = formatted.replace(/\n\n(شكراً|أتمنى|في الختام).*$/s, '');
        
        // تحسين التنسيق
        formatted = formatted.replace(/\n{3,}/g, '\n\n');
        formatted = formatted.trim();
        
        return formatted;
    }

    // تحديث مؤشر التقدم
    async updateProgress(step, message, callback) {
        this.currentStep = step;
        const percentage = Math.round((step / this.totalSteps) * 100);
        
        if (callback) {
            callback({
                step: step,
                total: this.totalSteps,
                percentage: percentage,
                message: message
            });
        }

        // تأخير قصير لإظهار التقدم
        await new Promise(resolve => setTimeout(resolve, 500));
    }

    // إيقاف مؤقت للعملية
    pauseGeneration() {
        this.isPaused = true;
    }

    // استئناف العملية
    resumeGeneration() {
        this.isPaused = false;
    }

    // إيقاف العملية نهائياً
    stopGeneration() {
        this.isPaused = true;
        this.isGenerating = false;
    }

    // توليد برومبت مخصص لمجال معين
    async generateDomainSpecificPrompt(userInput, domain, progressCallback) {
        const domainTemplates = {
            'برمجة': this.getProgrammingTemplate(),
            'تسويق رقمي': this.getMarketingTemplate(),
            'تصميم': this.getDesignTemplate(),
            'محتوى': this.getContentTemplate(),
            'سيو': this.getSEOTemplate()
        };

        const template = domainTemplates[domain] || this.getGeneralTemplate();
        
        const customPrompt = `${template}\n\nالمشروع المحدد: ${userInput}`;
        
        return await this.generateEnhancedPrompt(customPrompt, progressCallback);
    }

    // قوالب مخصصة للمجالات المختلفة
    getProgrammingTemplate() {
        return `قم بإنشاء برومبت احترافي لمشروع برمجي يتضمن:
- تحديد التقنيات المطلوبة
- هيكل قاعدة البيانات
- واجهات المستخدم
- APIs المطلوبة
- متطلبات الأمان
- خطة الاختبار
- التوثيق التقني`;
    }

    getMarketingTemplate() {
        return `قم بإنشاء برومبت احترافي لحملة تسويقية يتضمن:
- تحليل الجمهور المستهدف
- استراتيجية المحتوى
- قنوات التسويق
- الميزانية المطلوبة
- مؤشرات الأداء (KPIs)
- خطة زمنية للتنفيذ
- تحليل المنافسين`;
    }

    getDesignTemplate() {
        return `قم بإنشاء برومبت احترافي لمشروع تصميم يتضمن:
- الهوية البصرية
- دليل الألوان والخطوط
- تجربة المستخدم (UX)
- واجهة المستخدم (UI)
- التصميم التفاعلي
- الاستجابة للأجهزة المختلفة
- إرشادات الاستخدام`;
    }

    getContentTemplate() {
        return `قم بإنشاء برومبت احترافي لإنتاج المحتوى يتضمن:
- استراتيجية المحتوى
- أنواع المحتوى المطلوبة
- التقويم الزمني للنشر
- معايير الجودة
- تحسين محركات البحث
- قياس الأداء والتفاعل
- إدارة المجتمع`;
    }

    getSEOTemplate() {
        return `قم بإنشاء برومبت احترافي لتحسين محركات البحث يتضمن:
- تحليل الكلمات المفتاحية
- تحسين المحتوى
- البناء التقني للموقع
- استراتيجية الروابط
- تحليل المنافسين
- قياس الأداء
- التحسين المستمر`;
    }

    getGeneralTemplate() {
        return `قم بإنشاء برومبت احترافي ومتكامل يتضمن جميع الجوانب المهمة للمشروع`;
    }

    // حفظ البرومبت المولد
    saveGeneratedPrompt() {
        const history = this.getHistory();
        const newEntry = {
            id: Date.now(),
            prompt: this.generatedPrompt,
            timestamp: new Date().toISOString(),
            options: { ...this.currentOptions }
        };
        
        history.unshift(newEntry);
        
        // الاحتفاظ بآخر 50 برومبت فقط
        if (history.length > 50) {
            history.splice(50);
        }
        
        localStorage.setItem(CONFIG.STORAGE_KEYS.HISTORY, JSON.stringify(history));
    }

    // الحصول على تاريخ البرومبت المولدة
    getHistory() {
        const stored = localStorage.getItem(CONFIG.STORAGE_KEYS.HISTORY);
        return stored ? JSON.parse(stored) : [];
    }

    // مسح التاريخ
    clearHistory() {
        localStorage.removeItem(CONFIG.STORAGE_KEYS.HISTORY);
    }
}

// إنشاء مثيل عام من مولد البرومبت
const promptGenerator = new PromptGenerator();

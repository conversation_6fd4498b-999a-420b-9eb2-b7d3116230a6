<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مولد البرومبت</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
            text-align: right;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #results {
            background: white;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🧪 اختبار مولد البرومبت الاحترافي</h1>
    
    <div class="test-section">
        <h2>اختبار الوظائف الأساسية</h2>
        <button onclick="testBasicFunctions()">اختبار الوظائف الأساسية</button>
        <button onclick="testAPIManager()">اختبار مدير API</button>
        <button onclick="testPromptGenerator()">اختبار مولد البرومبت</button>
        <button onclick="testExportManager()">اختبار مدير التصدير</button>
        <button onclick="testSettingsManager()">اختبار مدير الإعدادات</button>
        <div id="results"></div>
    </div>

    <div class="test-section">
        <h2>اختبار توليد برومبت تجريبي</h2>
        <textarea id="testInput" rows="4" style="width: 100%; padding: 10px;" 
                  placeholder="أدخل وصف المشروع للاختبار...">أريد إنشاء موقع إلكتروني لبيع المنتجات الرقمية باستخدام HTML و CSS و JavaScript</textarea>
        <br><br>
        <button onclick="testPromptGeneration()">توليد برومبت تجريبي</button>
        <div id="promptResult"></div>
    </div>

    <div class="test-section">
        <h2>اختبار التصدير</h2>
        <button onclick="testHTMLExport()">اختبار تصدير HTML</button>
        <button onclick="testTXTExport()">اختبار تصدير TXT</button>
        <button onclick="testSettingsExport()">اختبار تصدير الإعدادات</button>
        <div id="exportResult"></div>
    </div>

    <!-- تضمين ملفات JavaScript -->
    <script src="js/config.js"></script>
    <script src="js/api-manager.js"></script>
    <script src="js/prompt-generator.js"></script>
    <script src="js/export-manager.js"></script>
    <script src="js/settings-manager.js"></script>

    <script>
        let testResults = document.getElementById('results');
        let promptResult = document.getElementById('promptResult');
        let exportResult = document.getElementById('exportResult');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : type === 'warning' ? 'warning' : '';
            testResults.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
        }

        function testBasicFunctions() {
            log('🔍 بدء اختبار الوظائف الأساسية...');
            
            // اختبار وجود المتغيرات العامة
            if (typeof CONFIG !== 'undefined') {
                log('✅ CONFIG متوفر', 'success');
            } else {
                log('❌ CONFIG غير متوفر', 'error');
            }

            if (typeof apiManager !== 'undefined') {
                log('✅ apiManager متوفر', 'success');
            } else {
                log('❌ apiManager غير متوفر', 'error');
            }

            if (typeof promptGenerator !== 'undefined') {
                log('✅ promptGenerator متوفر', 'success');
            } else {
                log('❌ promptGenerator غير متوفر', 'error');
            }

            if (typeof exportManager !== 'undefined') {
                log('✅ exportManager متوفر', 'success');
            } else {
                log('❌ exportManager غير متوفر', 'error');
            }

            if (typeof settingsManager !== 'undefined') {
                log('✅ settingsManager متوفر', 'success');
            } else {
                log('❌ settingsManager غير متوفر', 'error');
            }

            log('✅ انتهى اختبار الوظائف الأساسية');
        }

        function testAPIManager() {
            log('🔍 بدء اختبار مدير API...');
            
            try {
                // اختبار الحصول على أفضل مفتاح
                const bestKey = apiManager.getBestAvailableKey();
                log(`✅ أفضل مفتاح API: ${bestKey}`, 'success');

                // اختبار إحصائيات الاستخدام
                const stats = apiManager.getUsageStats();
                log(`📊 إحصائيات API: ${JSON.stringify(stats)}`, 'info');

                // اختبار تقسيم المحتوى
                const testContent = 'هذا نص تجريبي طويل '.repeat(100);
                const chunks = apiManager.splitContent(testContent);
                log(`📄 تم تقسيم المحتوى إلى ${chunks.length} جزء`, 'success');

                log('✅ انتهى اختبار مدير API بنجاح', 'success');
            } catch (error) {
                log(`❌ خطأ في اختبار مدير API: ${error.message}`, 'error');
            }
        }

        function testPromptGenerator() {
            log('🔍 بدء اختبار مولد البرومبت...');
            
            try {
                // اختبار الحصول على الخيارات المفعلة
                const enabledOptions = promptGenerator.getEnabledOptions();
                log(`⚙️ عدد الخيارات المفعلة: ${Object.keys(enabledOptions).length}`, 'info');

                // اختبار بناء البرومبت الأساسي
                const basePrompt = promptGenerator.buildBasePrompt('مشروع تجريبي');
                log(`📝 تم بناء برومبت أساسي بطول ${basePrompt.length} حرف`, 'success');

                // اختبار التاريخ
                const history = promptGenerator.getHistory();
                log(`📚 عدد البرومبت في التاريخ: ${history.length}`, 'info');

                log('✅ انتهى اختبار مولد البرومبت بنجاح', 'success');
            } catch (error) {
                log(`❌ خطأ في اختبار مولد البرومبت: ${error.message}`, 'error');
            }
        }

        function testExportManager() {
            log('🔍 بدء اختبار مدير التصدير...');
            
            try {
                // اختبار تنسيق المحتوى لـ HTML
                const testContent = '🧠 السياق: هذا اختبار\n🎯 الهدف: اختبار التصدير';
                const htmlContent = exportManager.formatHTMLContent(testContent);
                log(`🌐 تم تنسيق المحتوى لـ HTML`, 'success');

                // اختبار تحليل المحتوى لـ Excel
                const excelData = exportManager.parseContentForExcel(testContent);
                log(`📊 تم تحليل المحتوى لـ Excel: ${excelData.length} صف`, 'success');

                // اختبار تنسيق المحتوى النصي
                const textContent = exportManager.formatTextContent(testContent);
                log(`📄 تم تنسيق المحتوى النصي`, 'success');

                log('✅ انتهى اختبار مدير التصدير بنجاح', 'success');
            } catch (error) {
                log(`❌ خطأ في اختبار مدير التصدير: ${error.message}`, 'error');
            }
        }

        function testSettingsManager() {
            log('🔍 بدء اختبار مدير الإعدادات...');
            
            try {
                // اختبار الحصول على الإعدادات المفعلة
                const enabledSettings = settingsManager.getEnabledSettings();
                log(`⚙️ عدد الإعدادات المفعلة: ${Object.keys(enabledSettings).length}`, 'info');

                // اختبار إحصائيات الاستخدام
                const usageStats = settingsManager.getUsageStats();
                log(`📈 إحصائيات الاستخدام: ${JSON.stringify(usageStats)}`, 'info');

                // اختبار البحث في الإعدادات
                const searchResults = settingsManager.searchSettings('السياق');
                log(`🔍 نتائج البحث: ${searchResults.length} نتيجة`, 'success');

                log('✅ انتهى اختبار مدير الإعدادات بنجاح', 'success');
            } catch (error) {
                log(`❌ خطأ في اختبار مدير الإعدادات: ${error.message}`, 'error');
            }
        }

        async function testPromptGeneration() {
            const input = document.getElementById('testInput').value;
            if (!input.trim()) {
                promptResult.innerHTML = '<div class="error">يرجى إدخال وصف للمشروع</div>';
                return;
            }

            promptResult.innerHTML = '<div>🔄 جاري توليد البرومبت التجريبي...</div>';

            try {
                // بناء برومبت أساسي فقط (بدون استدعاء API)
                const basePrompt = promptGenerator.buildBasePrompt(input);
                promptResult.innerHTML = `
                    <div class="success">✅ تم بناء البرومبت الأساسي بنجاح!</div>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px; white-space: pre-wrap; max-height: 400px; overflow-y: auto;">${basePrompt}</div>
                `;
            } catch (error) {
                promptResult.innerHTML = `<div class="error">❌ خطأ: ${error.message}</div>`;
            }
        }

        function testHTMLExport() {
            const testContent = '🧠 السياق: هذا اختبار للتصدير\n🎯 الهدف: اختبار تصدير HTML\n📝 المحتوى: محتوى تجريبي للاختبار';
            
            try {
                exportManager.exportToHTML(testContent, 'اختبار_التصدير');
                exportResult.innerHTML = '<div class="success">✅ تم تصدير ملف HTML بنجاح!</div>';
            } catch (error) {
                exportResult.innerHTML = `<div class="error">❌ خطأ في التصدير: ${error.message}</div>`;
            }
        }

        function testTXTExport() {
            const testContent = '🧠 السياق: هذا اختبار للتصدير\n🎯 الهدف: اختبار تصدير TXT\n📝 المحتوى: محتوى تجريبي للاختبار';
            
            try {
                exportManager.exportToTXT(testContent, 'اختبار_التصدير');
                exportResult.innerHTML = '<div class="success">✅ تم تصدير ملف TXT بنجاح!</div>';
            } catch (error) {
                exportResult.innerHTML = `<div class="error">❌ خطأ في التصدير: ${error.message}</div>`;
            }
        }

        function testSettingsExport() {
            try {
                const settings = settingsManager.exportSettings();
                exportManager.exportSettings(settings);
                exportResult.innerHTML = '<div class="success">✅ تم تصدير الإعدادات بنجاح!</div>';
            } catch (error) {
                exportResult.innerHTML = `<div class="error">❌ خطأ في تصدير الإعدادات: ${error.message}</div>`;
            }
        }

        // تشغيل اختبار أساسي عند التحميل
        window.addEventListener('load', () => {
            log('🚀 بدء اختبارات مولد البرومبت الاحترافي...');
            testBasicFunctions();
        });
    </script>
</body>
</html>

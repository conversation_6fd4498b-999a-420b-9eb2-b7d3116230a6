// ===== إعدادات التطبيق الأساسية =====

const CONFIG = {
    // مفاتيح API الخاصة بـ Gemini
    GEMINI_API_KEYS: [
        'AIzaSyD4ouec6Q4EN1TbSi7LnQfKl9cqQEHegzQ',
        'AIzaSyDEdwpwXRAydelTq0BXHAfE3-tSlMga3xg',
        'AIzaSyCH5jU2QrczZOLPqNsceh_um6qs_N3Skgw',
        'AIzaSyCTYMFClrP1A1by2rTXTRYjl3mi-FfVvak',
        'AIzaSyDzSRdhUClIXUWpi9QRKi0hfB29_bzaQAc',
        'AIzaSyBzlVUgjCkG44mm3_8Rt-ZiLskXsxM29SM',
        'AIzaSyDeSqeSAb4QZ6JEf_LbLTrnVOgcGMSb7ds',
        'AIzaSyAu5ToET8qHvDUPzrDOvkr1T99qZXvZORk',
        'AIzaSyAFu5lHg2W5ibpFbm-k3kNlW-rI96JcQOg',
        'AIzaSyBThniUP9oFxyriMOV4-LpvmJtW3u1gZCQ',
        'AIzaSyAns8tVQ-V6sqbh7UPQB7tqYgWAZUIN6Z4',
        'AIzaSyCS32gY-wcVTRIuE2PY_3ppVloGmvFlAqo',
        'AIzaSyCc3Y4hHWuAxKtJx4E0h-spwYUP6Eqzk4k',
        'AIzaSyAmxg1f8A3s1mXxdUav0zeyC52BF-5QBoA',
        'AIzaSyD7C9Ry7EeCXwvrLkymXuUp3FwqbqloFuQ'
    ],

    // إعدادات API
    API_SETTINGS: {
        BASE_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent',
        MAX_TOKENS: 8000,
        TEMPERATURE: 0.7,
        TOP_P: 0.9,
        TOP_K: 40,
        TIMEOUT: 30000, // 30 ثانية
        RETRY_ATTEMPTS: 3,
        RETRY_DELAY: 2000 // 2 ثانية
    },

    // إعدادات تقسيم المحتوى
    CONTENT_SPLITTING: {
        MAX_CHUNK_SIZE: 6000, // أقصى حجم للقطعة الواحدة
        OVERLAP_SIZE: 200, // تداخل بين القطع
        MIN_CHUNK_SIZE: 100 // أقل حجم للقطعة
    },

    // خيارات البرومبت الافتراضية
    DEFAULT_PROMPT_OPTIONS: {
        context: {
            enabled: true,
            title: '🧠 السياق',
            description: 'تحديد السياق والخلفية للمشروع',
            defaultValue: 'أنا [المجال أو الوظيفة] وأعمل على [تحديد المشروع أو المشكلة أو الأداة]. أريد تطوير [اسم أو وصف الأداة أو المحتوى].'
        },
        mainGoal: {
            enabled: true,
            title: '🎯 الهدف الرئيسي',
            description: 'تحديد الهدف الأساسي من المشروع',
            defaultValue: 'الهدف من هذا المشروع هو [شرح ما تريد أن تحققه بدقة].'
        },
        domain: {
            enabled: true,
            title: '🧪 المجال أو التخصص',
            description: 'تحديد المجال المتخصص للمشروع',
            defaultValue: 'المجال هو [مثلاً: برمجة - سيو - تصميم - محتوى - تسويق رقمي ...].',
            options: ['برمجة', 'تسويق رقمي', 'تصميم', 'محتوى', 'سيو', 'تحليل بيانات', 'ذكاء صناعي', 'أخرى']
        },
        targetAudience: {
            enabled: true,
            title: '👥 الجمهور المستهدف',
            description: 'تحديد الفئة المستهدفة وخصائصها',
            defaultValue: 'الفئة المستهدفة هي [من هم؟ ما خبرتهم؟ لغتهم؟ مشاكلهم؟].'
        },
        inputs: {
            enabled: true,
            title: '📥 المدخلات',
            description: 'تحديد أنواع المدخلات المدعومة',
            defaultValue: 'يجب أن تدعم الأداة المدخلات التالية: [ملفات، نصوص، صور، كوبي وبيست، API inputs...].',
            options: ['ملفات نصية', 'صور', 'مقاطع فيديو', 'ملفات PDF', 'ملفات Excel', 'APIs', 'قواعد بيانات']
        },
        outputs: {
            enabled: true,
            title: '📤 المخرجات المطلوبة',
            description: 'تحديد نوع وصيغة المخرجات',
            defaultValue: '- [نوع التقرير أو النتيجة]\n- [صيغة الإخراج: HTML, Excel, JSON, PDF...]\n- [تصميم وتنسيق معين؟]',
            options: ['HTML', 'PDF', 'Excel', 'JSON', 'XML', 'CSV', 'Word']
        },
        design: {
            enabled: true,
            title: '📐 التنسيق والتصميم',
            description: 'متطلبات التصميم والتنسيق',
            defaultValue: '- تنسيق RTL أو LTR\n- خطوط معينة\n- ألوان مناسبة للمجال\n- تأثيرات CSS / Animation\n- تحسين تجربة المستخدم'
        },
        functions: {
            enabled: true,
            title: '⚙️ الوظائف المحددة',
            description: 'تحديد الوظائف الفرعية المطلوبة',
            defaultValue: '- [وصف كل وظيفة فرعية بدقة، كل واحدة كسطر منفصل]\n- ...'
        },
        aiIntegration: {
            enabled: true,
            title: '🧠 ذكاء صناعي مدمج',
            description: 'إعدادات دمج الذكاء الصناعي',
            defaultValue: '- استخدام [اسم API مثل Gemini أو ChatGPT أو Claude]\n- إدارة حدود التوكن من خلال تقسيم المحتوى\n- استدعاء متسلسل للوظائف'
        },
        advancedOptions: {
            enabled: true,
            title: '🧷 الخيارات المتقدمة',
            description: 'خيارات متقدمة للتحكم في الأداة',
            defaultValue: '- إمكانية تحديد المهام الفرعية المطلوب تنفيذها\n- حفظ الإعدادات واستيرادها\n- دعم تحليل الملفات الكبيرة\n- إدارة الأخطاء والمشاكل المتوقعة'
        },
        validation: {
            enabled: true,
            title: '📊 التحقق من النتائج',
            description: 'آليات التحقق من صحة النتائج',
            defaultValue: '- تحقق تلقائي من صحة البيانات\n- تأكيد خلو النتائج من المقدمات أو تكرار العبارات'
        },
        optimization: {
            enabled: true,
            title: '📈 التحسين المستمر',
            description: 'استراتيجيات التحسين والتطوير',
            defaultValue: '- تقسيم الكود والوظائف لوحدات قابلة للتحديث\n- سجل الأخطاء، لوحة متابعة الأداء'
        },
        programming: {
            enabled: true,
            title: '🔐 اللغة والبرمجة',
            description: 'متطلبات البرمجة واللغة',
            defaultValue: '- اللغة المستخدمة: [HTML / CSS / JS...]\n- يمنع استخدام: [React / Node.js...]\n- اللغة: [العربية / الإنجليزية]'
        },
        export: {
            enabled: true,
            title: '🧾 التصدير',
            description: 'خيارات التصدير المتاحة',
            defaultValue: '- HTML منسق + محرك بحث داخلي\n- Excel / PDF / TXT\n- ملف ZIP يحتوي جميع النتائج\n- حفظ واستيراد الإعدادات'
        },
        references: {
            enabled: false,
            title: '📎 روابط مرجعية أو أمثلة',
            description: 'روابط مرجعية أو أمثلة للاستعانة بها',
            defaultValue: '- [رابط لتصميم مشابه أو مثال مرجعي]'
        },
        performance: {
            enabled: true,
            title: '⏱️ متطلبات الأداء',
            description: 'متطلبات الأداء والسرعة',
            defaultValue: '- مدة التحليل المتوقعة لكل ملف/صفحة\n- عدد الملفات المدعومة في كل عملية'
        }
    },

    // إعدادات التصدير
    EXPORT_SETTINGS: {
        HTML: {
            template: 'modern',
            includeCSS: true,
            includeSearch: true,
            rtl: true
        },
        PDF: {
            format: 'A4',
            orientation: 'portrait',
            margin: '20mm',
            rtl: true,
            font: 'Cairo'
        },
        EXCEL: {
            sheetName: 'البرومبت المولد',
            includeFormatting: true,
            rtl: true
        }
    },

    // رسائل النظام
    MESSAGES: {
        LOADING: 'جاري معالجة طلبك...',
        GENERATING: 'جاري توليد البرومبت...',
        PROCESSING: 'جاري معالجة البيانات...',
        EXPORTING: 'جاري تصدير الملف...',
        SUCCESS: 'تم بنجاح!',
        ERROR: 'حدث خطأ، يرجى المحاولة مرة أخرى',
        API_ERROR: 'خطأ في الاتصال بالخدمة',
        VALIDATION_ERROR: 'يرجى التحقق من البيانات المدخلة',
        NETWORK_ERROR: 'خطأ في الاتصال بالإنترنت'
    },

    // إعدادات التخزين المحلي
    STORAGE_KEYS: {
        SETTINGS: 'promptGenerator_settings',
        HISTORY: 'promptGenerator_history',
        API_USAGE: 'promptGenerator_apiUsage',
        USER_PREFERENCES: 'promptGenerator_preferences'
    }
};

// تصدير الإعدادات للاستخدام في الملفات الأخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}

/* ===== الأنماط الأساسية ===== */
:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #059669;
    --info-color: #0891b2;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --dark-color: #1e293b;
    --light-color: #f8fafc;
    --border-radius: 12px;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
    text-align: right;
}

/* ===== Header Styles ===== */
.header-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 2rem 0;
    margin-bottom: 2rem;
    box-shadow: var(--box-shadow);
}

.main-title {
    font-family: 'Cairo', sans-serif;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    font-size: 2.5rem;
}

.subtitle {
    color: var(--secondary-color);
    font-size: 1.1rem;
    margin-bottom: 0;
}

.header-actions .btn {
    margin-left: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.header-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

/* ===== Main Content ===== */
.main-content {
    padding: 0 1rem;
}

.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    margin-bottom: 2rem;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    padding: 1.5rem;
    font-weight: 600;
}

.card-body {
    padding: 2rem;
}

/* ===== Form Styles ===== */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 2px solid #e2e8f0;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: var(--transition);
    font-family: 'Tajawal', sans-serif;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

/* ===== Button Styles ===== */
.btn {
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: var(--transition);
    font-family: 'Cairo', sans-serif;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #3b82f6);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1d4ed8, var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(37, 99, 235, 0.3);
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

/* ===== Loading Overlay ===== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-content {
    background: white;
    padding: 3rem;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* ===== Progress Bar ===== */
.progress {
    height: 1rem;
    border-radius: var(--border-radius);
    background-color: #e2e8f0;
}

.progress-bar {
    border-radius: var(--border-radius);
    background: linear-gradient(90deg, var(--primary-color), var(--success-color));
}

/* ===== Results Section ===== */
.generated-content {
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius);
    padding: 2rem;
    font-family: 'Tajawal', sans-serif;
    line-height: 1.8;
    white-space: pre-wrap;
    max-height: 600px;
    overflow-y: auto;
}

.export-options {
    border-top: 2px solid #e2e8f0;
    padding-top: 1.5rem;
}

.btn-group .btn {
    margin: 0.25rem;
}

/* ===== Footer ===== */
.footer-section {
    background: rgba(30, 41, 59, 0.95);
    color: white;
    padding: 2rem 0;
    margin-top: 3rem;
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .main-title {
        font-size: 2rem;
    }
    
    .header-actions {
        margin-top: 1rem;
        text-align: center;
    }
    
    .header-actions .btn {
        margin: 0.25rem;
        font-size: 0.9rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    .btn-group .btn {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }
}

@media (max-width: 576px) {
    .main-title {
        font-size: 1.8rem;
    }
    
    .loading-content {
        padding: 2rem;
        margin: 1rem;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        margin: 0.25rem 0;
    }
}

/* ===== Accessibility ===== */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* ===== Print Styles ===== */
@media print {
    .header-section,
    .footer-section,
    .export-options {
        display: none;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ccc;
    }

    body {
        background: white;
    }
}

/* ===== Dark Theme ===== */
.dark-theme {
    --primary-color: #3b82f6;
    --secondary-color: #94a3b8;
    --success-color: #10b981;
    --info-color: #06b6d4;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --dark-color: #f1f5f9;
    --light-color: #1e293b;
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    color: #f1f5f9;
}

.dark-theme .card {
    background: #334155;
    color: #f1f5f9;
}

.dark-theme .form-control,
.dark-theme .form-select {
    background: #475569;
    border-color: #64748b;
    color: #f1f5f9;
}

.dark-theme .form-control:focus,
.dark-theme .form-select:focus {
    background: #475569;
    border-color: var(--primary-color);
    color: #f1f5f9;
}

/* ===== No Animations ===== */
.no-animations *,
.no-animations *::before,
.no-animations *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
}

/* ===== Mobile Optimizations ===== */
.mobile-view .main-title {
    font-size: 1.5rem;
}

.mobile-view .card-body {
    padding: 1rem;
}

.mobile-view .btn-group {
    flex-direction: column;
}

.mobile-view .btn-group .btn {
    margin: 0.25rem 0;
    width: 100%;
}

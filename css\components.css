/* ===== مكونات الخيارات ===== */
.option-group {
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: var(--transition);
}

.option-group:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.1);
}

.option-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    cursor: pointer;
}

.option-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.option-toggle {
    position: relative;
    width: 60px;
    height: 30px;
    background: #cbd5e1;
    border-radius: 15px;
    transition: var(--transition);
    cursor: pointer;
}

.option-toggle.active {
    background: var(--primary-color);
}

.option-toggle::after {
    content: '';
    position: absolute;
    top: 3px;
    right: 3px;
    width: 24px;
    height: 24px;
    background: white;
    border-radius: 50%;
    transition: var(--transition);
}

.option-toggle.active::after {
    transform: translateX(-30px);
}

.option-content {
    display: none;
    animation: slideDown 0.3s ease;
}

.option-content.active {
    display: block;
}

.option-description {
    color: var(--secondary-color);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.6;
}

/* ===== خيارات فرعية ===== */
.sub-option {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.sub-option-header {
    display: flex;
    align-items: center;
    justify-content: between;
    margin-bottom: 0.5rem;
}

.sub-option-title {
    font-weight: 500;
    color: var(--dark-color);
    margin: 0;
    flex-grow: 1;
}

.sub-option-checkbox {
    margin-left: 0.5rem;
    transform: scale(1.2);
}

/* ===== مؤشر التقدم المتقدم ===== */
.advanced-progress {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin: 1rem 0;
    box-shadow: var(--box-shadow);
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.progress-title {
    font-weight: 600;
    color: var(--dark-color);
}

.progress-percentage {
    font-weight: 700;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.progress-steps {
    margin-top: 1rem;
}

.progress-step {
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f5f9;
}

.progress-step:last-child {
    border-bottom: none;
}

.step-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
    font-size: 0.8rem;
}

.step-icon.completed {
    background: var(--success-color);
    color: white;
}

.step-icon.current {
    background: var(--primary-color);
    color: white;
}

.step-icon.pending {
    background: #e2e8f0;
    color: var(--secondary-color);
}

.step-text {
    flex-grow: 1;
    color: var(--dark-color);
}

.step-text.completed {
    color: var(--success-color);
}

.step-text.current {
    color: var(--primary-color);
    font-weight: 600;
}

/* ===== أزرار التحكم ===== */
.control-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin: 1.5rem 0;
    flex-wrap: wrap;
}

.control-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: var(--transition);
    cursor: pointer;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.control-btn.pause {
    background: var(--warning-color);
    color: white;
}

.control-btn.resume {
    background: var(--success-color);
    color: white;
}

.control-btn.stop {
    background: var(--danger-color);
    color: white;
}

/* ===== نتائج مفصلة ===== */
.result-section {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--box-shadow);
}

.result-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f1f5f9;
}

.result-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
}

.result-actions {
    display: flex;
    gap: 0.5rem;
}

.result-content {
    line-height: 1.8;
    color: var(--dark-color);
}

.result-content h1,
.result-content h2,
.result-content h3,
.result-content h4,
.result-content h5,
.result-content h6 {
    color: var(--primary-color);
    margin: 1.5rem 0 1rem 0;
}

.result-content ul,
.result-content ol {
    padding-right: 2rem;
    margin: 1rem 0;
}

.result-content li {
    margin: 0.5rem 0;
}

.result-content blockquote {
    background: #f8fafc;
    border-right: 4px solid var(--primary-color);
    padding: 1rem 1.5rem;
    margin: 1.5rem 0;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

/* ===== محرك البحث ===== */
.search-container {
    position: relative;
    margin-bottom: 1.5rem;
}

.search-input {
    width: 100%;
    padding: 0.75rem 3rem 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.search-input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--secondary-color);
}

.search-results {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: var(--border-radius);
    max-height: 300px;
    overflow-y: auto;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: var(--box-shadow);
}

.search-result-item {
    padding: 1rem;
    border-bottom: 1px solid #f1f5f9;
    cursor: pointer;
    transition: var(--transition);
}

.search-result-item:hover {
    background: #f8fafc;
}

.search-result-item:last-child {
    border-bottom: none;
}

/* ===== إعدادات متقدمة ===== */
.settings-panel {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--box-shadow);
}

.settings-group {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
}

.settings-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.settings-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 0;
}

.setting-label {
    font-weight: 500;
    color: var(--dark-color);
}

.setting-control {
    min-width: 200px;
}

/* ===== تأثيرات خاصة ===== */
.glow-effect {
    box-shadow: 0 0 20px rgba(37, 99, 235, 0.3);
}

.pulse-effect {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(37, 99, 235, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(37, 99, 235, 0);
    }
}

// ===== مدير الإعدادات والتفضيلات =====

class SettingsManager {
    constructor() {
        this.settings = this.loadSettings();
        this.userPreferences = this.loadUserPreferences();
    }

    // تحميل الإعدادات من التخزين المحلي
    loadSettings() {
        const stored = localStorage.getItem(CONFIG.STORAGE_KEYS.SETTINGS);
        if (stored) {
            try {
                const parsedSettings = JSON.parse(stored);
                // دمج الإعدادات المحفوظة مع الافتراضية
                return this.mergeWithDefaults(parsedSettings);
            } catch (error) {
                console.error('خطأ في تحميل الإعدادات:', error);
                return { ...CONFIG.DEFAULT_PROMPT_OPTIONS };
            }
        }
        return { ...CONFIG.DEFAULT_PROMPT_OPTIONS };
    }

    // تحميل تفضيلات المستخدم
    loadUserPreferences() {
        const stored = localStorage.getItem(CONFIG.STORAGE_KEYS.USER_PREFERENCES);
        if (stored) {
            try {
                return JSON.parse(stored);
            } catch (error) {
                console.error('خطأ في تحميل التفضيلات:', error);
                return this.getDefaultPreferences();
            }
        }
        return this.getDefaultPreferences();
    }

    // الحصول على التفضيلات الافتراضية
    getDefaultPreferences() {
        return {
            theme: 'light',
            language: 'ar',
            autoSave: true,
            showProgress: true,
            enableAnimations: true,
            defaultExportFormat: 'html',
            maxHistoryItems: 50,
            apiTimeout: 30000,
            retryAttempts: 3
        };
    }

    // دمج الإعدادات مع الافتراضية
    mergeWithDefaults(userSettings) {
        const merged = { ...CONFIG.DEFAULT_PROMPT_OPTIONS };
        
        Object.keys(userSettings).forEach(key => {
            if (merged[key]) {
                merged[key] = { ...merged[key], ...userSettings[key] };
            }
        });
        
        return merged;
    }

    // حفظ الإعدادات
    saveSettings(settings = null) {
        const toSave = settings || this.settings;
        localStorage.setItem(CONFIG.STORAGE_KEYS.SETTINGS, JSON.stringify(toSave));
        this.settings = toSave;
        
        // تحديث إعدادات مولد البرومبت
        if (promptGenerator) {
            promptGenerator.updateOptions(toSave);
        }
    }

    // حفظ تفضيلات المستخدم
    saveUserPreferences(preferences = null) {
        const toSave = preferences || this.userPreferences;
        localStorage.setItem(CONFIG.STORAGE_KEYS.USER_PREFERENCES, JSON.stringify(toSave));
        this.userPreferences = toSave;
    }

    // تحديث إعداد واحد
    updateSetting(key, value) {
        if (this.settings[key]) {
            this.settings[key] = { ...this.settings[key], ...value };
            this.saveSettings();
        }
    }

    // تحديث تفضيل واحد
    updatePreference(key, value) {
        this.userPreferences[key] = value;
        this.saveUserPreferences();
    }

    // تفعيل/إلغاء تفعيل خيار
    toggleOption(optionKey) {
        if (this.settings[optionKey]) {
            this.settings[optionKey].enabled = !this.settings[optionKey].enabled;
            this.saveSettings();
            return this.settings[optionKey].enabled;
        }
        return false;
    }

    // تحديث قيمة افتراضية لخيار
    updateDefaultValue(optionKey, newValue) {
        if (this.settings[optionKey]) {
            this.settings[optionKey].defaultValue = newValue;
            this.saveSettings();
        }
    }

    // إعادة تعيين الإعدادات للافتراضية
    resetToDefaults() {
        this.settings = { ...CONFIG.DEFAULT_PROMPT_OPTIONS };
        this.userPreferences = this.getDefaultPreferences();
        this.saveSettings();
        this.saveUserPreferences();
    }

    // إعادة تعيين خيار واحد للافتراضي
    resetOptionToDefault(optionKey) {
        if (CONFIG.DEFAULT_PROMPT_OPTIONS[optionKey]) {
            this.settings[optionKey] = { ...CONFIG.DEFAULT_PROMPT_OPTIONS[optionKey] };
            this.saveSettings();
        }
    }

    // تصدير الإعدادات
    exportSettings() {
        const exportData = {
            version: '1.0',
            exportedAt: new Date().toISOString(),
            settings: this.settings,
            preferences: this.userPreferences,
            metadata: {
                userAgent: navigator.userAgent,
                language: navigator.language,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
            }
        };
        
        return exportData;
    }

    // استيراد الإعدادات
    async importSettings(settingsData) {
        try {
            if (settingsData.settings) {
                this.settings = this.mergeWithDefaults(settingsData.settings);
                this.saveSettings();
            }
            
            if (settingsData.preferences) {
                this.userPreferences = { ...this.getDefaultPreferences(), ...settingsData.preferences };
                this.saveUserPreferences();
            }
            
            return true;
        } catch (error) {
            console.error('خطأ في استيراد الإعدادات:', error);
            throw new Error('فشل في استيراد الإعدادات');
        }
    }

    // التحقق من صحة الإعدادات
    validateSettings(settings) {
        const errors = [];
        
        Object.keys(settings).forEach(key => {
            const setting = settings[key];
            
            if (!setting.title || !setting.description) {
                errors.push(`إعداد ${key} غير مكتمل`);
            }
            
            if (typeof setting.enabled !== 'boolean') {
                errors.push(`حالة التفعيل لـ ${key} غير صحيحة`);
            }
        });
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    // الحصول على إحصائيات الاستخدام
    getUsageStats() {
        const history = promptGenerator ? promptGenerator.getHistory() : [];
        const apiStats = apiManager ? apiManager.getUsageStats() : {};
        
        return {
            totalPrompts: history.length,
            lastUsed: history.length > 0 ? history[0].timestamp : null,
            apiRequests: apiStats.totalRequests || 0,
            apiErrors: apiStats.totalErrors || 0,
            settingsLastModified: localStorage.getItem(CONFIG.STORAGE_KEYS.SETTINGS + '_modified'),
            storageUsed: this.calculateStorageUsage()
        };
    }

    // حساب استخدام التخزين
    calculateStorageUsage() {
        let totalSize = 0;
        
        Object.values(CONFIG.STORAGE_KEYS).forEach(key => {
            const item = localStorage.getItem(key);
            if (item) {
                totalSize += item.length;
            }
        });
        
        return {
            bytes: totalSize,
            kb: Math.round(totalSize / 1024 * 100) / 100,
            mb: Math.round(totalSize / (1024 * 1024) * 100) / 100
        };
    }

    // تنظيف البيانات القديمة
    cleanupOldData() {
        const maxAge = 30 * 24 * 60 * 60 * 1000; // 30 يوم
        const now = Date.now();
        
        // تنظيف التاريخ القديم
        const history = promptGenerator ? promptGenerator.getHistory() : [];
        const filteredHistory = history.filter(item => {
            const itemDate = new Date(item.timestamp).getTime();
            return (now - itemDate) < maxAge;
        });
        
        if (filteredHistory.length !== history.length) {
            localStorage.setItem(CONFIG.STORAGE_KEYS.HISTORY, JSON.stringify(filteredHistory));
        }
        
        // تنظيف إحصائيات API القديمة
        if (apiManager) {
            const apiUsage = apiManager.apiUsage;
            Object.keys(apiUsage).forEach(keyIndex => {
                const usage = apiUsage[keyIndex];
                if (usage.lastUsed && (now - usage.lastUsed) > maxAge) {
                    apiManager.resetKeyStats(keyIndex);
                }
            });
        }
    }

    // إنشاء نسخة احتياطية
    createBackup() {
        const backup = {
            timestamp: new Date().toISOString(),
            settings: this.settings,
            preferences: this.userPreferences,
            history: promptGenerator ? promptGenerator.getHistory() : [],
            apiUsage: apiManager ? apiManager.apiUsage : {},
            version: '1.0'
        };
        
        return backup;
    }

    // استعادة من النسخة الاحتياطية
    async restoreFromBackup(backupData) {
        try {
            if (backupData.settings) {
                this.settings = this.mergeWithDefaults(backupData.settings);
                this.saveSettings();
            }
            
            if (backupData.preferences) {
                this.userPreferences = { ...this.getDefaultPreferences(), ...backupData.preferences };
                this.saveUserPreferences();
            }
            
            if (backupData.history) {
                localStorage.setItem(CONFIG.STORAGE_KEYS.HISTORY, JSON.stringify(backupData.history));
            }
            
            if (backupData.apiUsage && apiManager) {
                apiManager.apiUsage = backupData.apiUsage;
                apiManager.saveAPIUsage();
            }
            
            return true;
        } catch (error) {
            console.error('خطأ في استعادة النسخة الاحتياطية:', error);
            throw new Error('فشل في استعادة النسخة الاحتياطية');
        }
    }

    // الحصول على الإعدادات المفعلة فقط
    getEnabledSettings() {
        const enabled = {};
        Object.keys(this.settings).forEach(key => {
            if (this.settings[key].enabled) {
                enabled[key] = this.settings[key];
            }
        });
        return enabled;
    }

    // البحث في الإعدادات
    searchSettings(query) {
        const results = [];
        const searchTerm = query.toLowerCase();
        
        Object.keys(this.settings).forEach(key => {
            const setting = this.settings[key];
            if (
                setting.title.toLowerCase().includes(searchTerm) ||
                setting.description.toLowerCase().includes(searchTerm) ||
                setting.defaultValue.toLowerCase().includes(searchTerm)
            ) {
                results.push({
                    key: key,
                    setting: setting,
                    relevance: this.calculateRelevance(setting, searchTerm)
                });
            }
        });
        
        return results.sort((a, b) => b.relevance - a.relevance);
    }

    // حساب مدى الصلة للبحث
    calculateRelevance(setting, searchTerm) {
        let score = 0;
        
        if (setting.title.toLowerCase().includes(searchTerm)) score += 10;
        if (setting.description.toLowerCase().includes(searchTerm)) score += 5;
        if (setting.defaultValue.toLowerCase().includes(searchTerm)) score += 3;
        
        return score;
    }

    // تطبيق الإعدادات على الواجهة
    applySettingsToUI() {
        // تطبيق السمة
        if (this.userPreferences.theme === 'dark') {
            document.body.classList.add('dark-theme');
        } else {
            document.body.classList.remove('dark-theme');
        }
        
        // تطبيق الحركات
        if (!this.userPreferences.enableAnimations) {
            document.body.classList.add('no-animations');
        } else {
            document.body.classList.remove('no-animations');
        }
        
        // تطبيق اللغة
        document.documentElement.lang = this.userPreferences.language;
        document.documentElement.dir = this.userPreferences.language === 'ar' ? 'rtl' : 'ltr';
    }
}

// إنشاء مثيل عام من مدير الإعدادات
const settingsManager = new SettingsManager();

// ===== مدير التصدير للملفات المختلفة =====

class ExportManager {
    constructor() {
        this.exportSettings = CONFIG.EXPORT_SETTINGS;
    }

    // تصدير إلى HTML
    async exportToHTML(content, filename = 'البرومبت_المولد') {
        const htmlTemplate = this.getHTMLTemplate(content);
        this.downloadFile(htmlTemplate, `${filename}.html`, 'text/html');
    }

    // تصدير إلى PDF
    async exportToPDF(content, filename = 'البرومبت_المولد') {
        try {
            // استخدام مكتبة jsPDF مع دعم العربية
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF({
                orientation: this.exportSettings.PDF.orientation,
                unit: 'mm',
                format: this.exportSettings.PDF.format
            });

            // إعداد الخط العربي
            doc.setFont('Cairo', 'normal');
            doc.setFontSize(12);
            doc.setR2L(true);

            // تقسيم النص إلى أسطر
            const lines = this.splitTextForPDF(content, doc);
            
            let yPosition = 20;
            const lineHeight = 7;
            const pageHeight = doc.internal.pageSize.height;
            const margin = 20;

            lines.forEach(line => {
                if (yPosition > pageHeight - margin) {
                    doc.addPage();
                    yPosition = 20;
                }
                
                doc.text(line, doc.internal.pageSize.width - margin, yPosition, {
                    align: 'right',
                    maxWidth: doc.internal.pageSize.width - (margin * 2)
                });
                
                yPosition += lineHeight;
            });

            doc.save(`${filename}.pdf`);
        } catch (error) {
            console.error('خطأ في تصدير PDF:', error);
            // استخدام طريقة بديلة
            this.exportToPDFAlternative(content, filename);
        }
    }

    // طريقة بديلة لتصدير PDF
    exportToPDFAlternative(content, filename) {
        const printWindow = window.open('', '_blank');
        const htmlContent = this.getPDFHTMLTemplate(content);
        
        printWindow.document.write(htmlContent);
        printWindow.document.close();
        
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 500);
    }

    // تصدير إلى Excel
    async exportToExcel(content, filename = 'البرومبت_المولد') {
        try {
            // تحويل المحتوى إلى بيانات جدولية
            const data = this.parseContentForExcel(content);
            
            // إنشاء workbook
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.aoa_to_sheet(data);
            
            // إعداد اتجاه RTL
            ws['!dir'] = 'rtl';
            
            // إضافة الورقة إلى الكتاب
            XLSX.utils.book_append_sheet(wb, ws, this.exportSettings.EXCEL.sheetName);
            
            // تصدير الملف
            XLSX.writeFile(wb, `${filename}.xlsx`);
        } catch (error) {
            console.error('خطأ في تصدير Excel:', error);
            // استخدام طريقة بديلة
            this.exportToCSV(content, filename);
        }
    }

    // تصدير إلى CSV كبديل لـ Excel
    exportToCSV(content, filename) {
        const data = this.parseContentForCSV(content);
        const csvContent = '\uFEFF' + data.map(row => 
            row.map(cell => `"${cell.replace(/"/g, '""')}"`).join(',')
        ).join('\n');
        
        this.downloadFile(csvContent, `${filename}.csv`, 'text/csv');
    }

    // تصدير إلى TXT
    exportToTXT(content, filename = 'البرومبت_المولد') {
        const formattedContent = this.formatTextContent(content);
        this.downloadFile(formattedContent, `${filename}.txt`, 'text/plain');
    }

    // تصدير إلى ZIP
    async exportToZIP(content, filename = 'البرومبت_المولد') {
        try {
            const zip = new JSZip();
            
            // إضافة ملفات مختلفة إلى الأرشيف
            zip.file(`${filename}.html`, this.getHTMLTemplate(content));
            zip.file(`${filename}.txt`, this.formatTextContent(content));
            
            // إضافة ملف JSON للإعدادات
            const settings = {
                generatedAt: new Date().toISOString(),
                options: promptGenerator.currentOptions,
                content: content
            };
            zip.file('settings.json', JSON.stringify(settings, null, 2));
            
            // إضافة ملف README
            zip.file('README.txt', this.getReadmeContent());
            
            // توليد وتحميل الأرشيف
            const zipBlob = await zip.generateAsync({ type: 'blob' });
            this.downloadBlob(zipBlob, `${filename}.zip`);
            
        } catch (error) {
            console.error('خطأ في إنشاء ملف ZIP:', error);
            alert('حدث خطأ في إنشاء الملف المضغوط');
        }
    }

    // قالب HTML للتصدير
    getHTMLTemplate(content) {
        return `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>البرومبت المولد</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.8;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
            color: #1e293b;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 20px;
        }
        .title {
            color: #2563eb;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #64748b;
            font-size: 1.1rem;
        }
        .content {
            white-space: pre-wrap;
            line-height: 1.8;
        }
        .search-container {
            margin-bottom: 20px;
            position: relative;
        }
        .search-input {
            width: 100%;
            padding: 12px 40px 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            font-family: 'Cairo', sans-serif;
        }
        .search-input:focus {
            outline: none;
            border-color: #2563eb;
        }
        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #64748b;
        }
        .highlight {
            background-color: #fef08a;
            padding: 2px 4px;
            border-radius: 4px;
        }
        @media print {
            .search-container { display: none; }
            body { background: white; }
            .container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">البرومبت المولد</h1>
            <p class="subtitle">تم إنشاؤه في ${new Date().toLocaleDateString('ar-SA')}</p>
        </div>
        
        <div class="search-container">
            <input type="text" class="search-input" placeholder="البحث في المحتوى..." id="searchInput">
            <span class="search-icon">🔍</span>
        </div>
        
        <div class="content" id="content">${this.formatHTMLContent(content)}</div>
    </div>
    
    <script>
        // محرك البحث
        const searchInput = document.getElementById('searchInput');
        const content = document.getElementById('content');
        const originalContent = content.innerHTML;
        
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.trim();
            
            if (searchTerm === '') {
                content.innerHTML = originalContent;
                return;
            }
            
            const regex = new RegExp(searchTerm, 'gi');
            const highlightedContent = originalContent.replace(regex, '<span class="highlight">$&</span>');
            content.innerHTML = highlightedContent;
        });
    </script>
</body>
</html>`;
    }

    // قالب HTML للطباعة
    getPDFHTMLTemplate(content) {
        return `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>البرومبت المولد</title>
    <style>
        @page { margin: 2cm; }
        body {
            font-family: 'Cairo', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            direction: rtl;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #ccc;
            padding-bottom: 15px;
        }
        .content {
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>البرومبت المولد</h1>
        <p>تم إنشاؤه في ${new Date().toLocaleDateString('ar-SA')}</p>
    </div>
    <div class="content">${this.formatHTMLContent(content)}</div>
</body>
</html>`;
    }

    // تنسيق المحتوى لـ HTML
    formatHTMLContent(content) {
        return content
            .replace(/\n/g, '<br>')
            .replace(/🧠|🎯|🧪|👥|📥|📤|📐|⚙️|🧠|🧷|📊|📈|🔐|🧾|📎|⏱️/g, '<strong>$&</strong>');
    }

    // تحليل المحتوى لـ Excel
    parseContentForExcel(content) {
        const lines = content.split('\n').filter(line => line.trim());
        const data = [['القسم', 'المحتوى']];
        
        let currentSection = '';
        
        lines.forEach(line => {
            if (line.match(/^[🧠🎯🧪👥📥📤📐⚙️🧷📊📈🔐🧾📎⏱️]/)) {
                currentSection = line.trim();
            } else if (line.trim() && currentSection) {
                data.push([currentSection, line.trim()]);
            }
        });
        
        return data;
    }

    // تحليل المحتوى لـ CSV
    parseContentForCSV(content) {
        return this.parseContentForExcel(content);
    }

    // تنسيق المحتوى النصي
    formatTextContent(content) {
        const header = `البرومبت المولد
تم إنشاؤه في ${new Date().toLocaleDateString('ar-SA')}
${'='.repeat(50)}

`;
        return header + content;
    }

    // محتوى ملف README
    getReadmeContent() {
        return `ملف البرومبت المولد
==================

هذا الأرشيف يحتوي على:

1. البرومبت المولد بصيغة HTML (مع محرك بحث)
2. البرومبت المولد بصيغة نصية
3. ملف الإعدادات (JSON)
4. هذا الملف التوضيحي

تاريخ الإنشاء: ${new Date().toLocaleDateString('ar-SA')}
الوقت: ${new Date().toLocaleTimeString('ar-SA')}

للاستفادة من الملفات:
- افتح ملف HTML في المتصفح للعرض التفاعلي
- استخدم الملف النصي للنسخ واللصق
- ملف الإعدادات يمكن استيراده مرة أخرى في الأداة

شكراً لاستخدام مولد البرومبت الاحترافي!`;
    }

    // تقسيم النص لـ PDF
    splitTextForPDF(text, doc) {
        const lines = text.split('\n');
        const result = [];
        
        lines.forEach(line => {
            if (line.trim()) {
                const wrappedLines = doc.splitTextToSize(line, doc.internal.pageSize.width - 40);
                result.push(...wrappedLines);
            } else {
                result.push('');
            }
        });
        
        return result;
    }

    // تحميل ملف
    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType + ';charset=utf-8' });
        this.downloadBlob(blob, filename);
    }

    // تحميل Blob
    downloadBlob(blob, filename) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // تصدير الإعدادات
    exportSettings(settings) {
        const settingsData = {
            version: '1.0',
            exportedAt: new Date().toISOString(),
            settings: settings
        };
        
        const content = JSON.stringify(settingsData, null, 2);
        this.downloadFile(content, 'prompt_generator_settings.json', 'application/json');
    }

    // استيراد الإعدادات
    async importSettings(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);
                    if (data.settings) {
                        resolve(data.settings);
                    } else {
                        reject(new Error('ملف الإعدادات غير صالح'));
                    }
                } catch (error) {
                    reject(new Error('خطأ في قراءة ملف الإعدادات'));
                }
            };
            
            reader.onerror = () => reject(new Error('خطأ في قراءة الملف'));
            reader.readAsText(file);
        });
    }
}

// إنشاء مثيل عام من مدير التصدير
const exportManager = new ExportManager();

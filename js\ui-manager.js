// ===== مدير واجهة المستخدم والتفاعل =====

class UIManager {
    constructor() {
        this.isInitialized = false;
        this.currentProgress = 0;
        this.isPaused = false;
        this.elements = {};
    }

    // تهيئة واجهة المستخدم
    init() {
        if (this.isInitialized) return;

        this.cacheElements();
        this.bindEvents();
        this.loadPromptOptions();
        this.applyUserPreferences();
        this.setupAutoSave();
        this.restoreAutoSaved();
        this.cleanupTempData();

        // إظهار نصائح للمستخدمين الجدد
        setTimeout(() => {
            if (this.isFirstTimeUser()) {
                this.showTips();
            }
        }, 2000);

        this.isInitialized = true;
        console.log('تم تهيئة واجهة المستخدم بنجاح');
    }

    // إعداد الحفظ التلقائي
    setupAutoSave() {
        if (this.elements.mainPrompt) {
            this.elements.mainPrompt.addEventListener('input', () => {
                clearTimeout(this.autoSaveTimeout);
                this.autoSaveTimeout = setTimeout(() => {
                    this.autoSave();
                }, 2000); // حفظ بعد ثانيتين من التوقف عن الكتابة
            });
        }
    }

    // التحقق من المستخدم الجديد
    isFirstTimeUser() {
        const hasUsed = localStorage.getItem('has_used_app');
        if (!hasUsed) {
            localStorage.setItem('has_used_app', 'true');
            return true;
        }
        return false;
    }

    // تخزين مؤقت للعناصر
    cacheElements() {
        this.elements = {
            mainPrompt: document.getElementById('mainPrompt'),
            promptOptions: document.getElementById('promptOptions'),
            generateBtn: document.getElementById('generateBtn'),
            resultsSection: document.getElementById('resultsSection'),
            generatedPrompt: document.getElementById('generatedPrompt'),
            loadingOverlay: document.getElementById('loadingOverlay'),
            progressBar: document.getElementById('progressBar'),
            progressText: document.getElementById('progressText'),
            pauseBtn: document.getElementById('pauseBtn'),
            importSettings: document.getElementById('importSettings'),
            exportSettings: document.getElementById('exportSettings'),
            settingsFileInput: document.getElementById('settingsFileInput'),
            exportHTML: document.getElementById('exportHTML'),
            exportExcel: document.getElementById('exportExcel'),
            exportPDF: document.getElementById('exportPDF'),
            exportTXT: document.getElementById('exportTXT'),
            exportZIP: document.getElementById('exportZIP')
        };
    }

    // ربط الأحداث
    bindEvents() {
        // زر التوليد
        this.elements.generateBtn?.addEventListener('click', () => this.handleGenerate());
        
        // أزرار التصدير
        this.elements.exportHTML?.addEventListener('click', () => this.handleExport('html'));
        this.elements.exportExcel?.addEventListener('click', () => this.handleExport('excel'));
        this.elements.exportPDF?.addEventListener('click', () => this.handleExport('pdf'));
        this.elements.exportTXT?.addEventListener('click', () => this.handleExport('txt'));
        this.elements.exportZIP?.addEventListener('click', () => this.handleExport('zip'));
        
        // إدارة الإعدادات
        this.elements.importSettings?.addEventListener('click', () => this.handleImportSettings());
        this.elements.exportSettings?.addEventListener('click', () => this.handleExportSettings());
        this.elements.settingsFileInput?.addEventListener('change', (e) => this.handleSettingsFileSelect(e));
        
        // زر الإيقاف المؤقت
        this.elements.pauseBtn?.addEventListener('click', () => this.handlePauseResume());
        
        // تفاعل مع خيارات البرومبت
        this.elements.promptOptions?.addEventListener('change', (e) => this.handleOptionChange(e));
        this.elements.promptOptions?.addEventListener('click', (e) => this.handleOptionToggle(e));
    }

    // تحميل خيارات البرومبت
    loadPromptOptions() {
        if (!this.elements.promptOptions) return;
        
        const options = settingsManager.settings;
        let html = '';
        
        Object.keys(options).forEach(key => {
            const option = options[key];
            html += this.createOptionHTML(key, option);
        });
        
        this.elements.promptOptions.innerHTML = html;
        this.addOptionAnimations();
    }

    // إنشاء HTML للخيار
    createOptionHTML(key, option) {
        return `
            <div class="option-group animate-fadeIn" data-option="${key}">
                <div class="option-header" onclick="uiManager.toggleOptionContent('${key}')">
                    <h4 class="option-title">
                        <span class="option-icon">${option.title.split(' ')[0]}</span>
                        ${option.title.substring(option.title.indexOf(' ') + 1)}
                    </h4>
                    <div class="option-toggle ${option.enabled ? 'active' : ''}" 
                         onclick="event.stopPropagation(); uiManager.toggleOption('${key}')">
                    </div>
                </div>
                <div class="option-content ${option.enabled ? 'active' : ''}" id="content-${key}">
                    <p class="option-description">${option.description}</p>
                    <div class="form-group">
                        <label for="input-${key}" class="form-label">المحتوى:</label>
                        ${this.createOptionInput(key, option)}
                    </div>
                    ${option.options ? this.createOptionSelect(key, option) : ''}
                </div>
            </div>
        `;
    }

    // إنشاء حقل الإدخال للخيار
    createOptionInput(key, option) {
        const lines = option.defaultValue.split('\n').length;
        const rows = Math.max(2, Math.min(6, lines));
        
        return `
            <textarea 
                id="input-${key}" 
                class="form-control" 
                rows="${rows}"
                placeholder="${option.description}"
                data-option="${key}"
            >${option.defaultValue}</textarea>
        `;
    }

    // إنشاء قائمة الخيارات
    createOptionSelect(key, option) {
        if (!option.options || !Array.isArray(option.options)) return '';
        
        let html = `
            <div class="form-group mt-3">
                <label for="select-${key}" class="form-label">خيارات سريعة:</label>
                <select id="select-${key}" class="form-select" data-option="${key}">
                    <option value="">اختر خياراً...</option>
        `;
        
        option.options.forEach(opt => {
            html += `<option value="${opt}">${opt}</option>`;
        });
        
        html += `</select></div>`;
        return html;
    }

    // تفعيل/إلغاء تفعيل خيار
    toggleOption(key) {
        const isEnabled = settingsManager.toggleOption(key);
        const optionGroup = document.querySelector(`[data-option="${key}"]`);
        const toggle = optionGroup?.querySelector('.option-toggle');
        const content = optionGroup?.querySelector('.option-content');
        
        if (toggle) {
            toggle.classList.toggle('active', isEnabled);
        }
        
        if (content) {
            content.classList.toggle('active', isEnabled);
            if (isEnabled) {
                content.style.display = 'block';
                content.classList.add('animate-slideDown');
            } else {
                content.classList.add('animate-slideUp');
                setTimeout(() => {
                    content.style.display = 'none';
                }, 300);
            }
        }
    }

    // تبديل محتوى الخيار
    toggleOptionContent(key) {
        const content = document.getElementById(`content-${key}`);
        const option = settingsManager.settings[key];
        
        if (option && option.enabled && content) {
            const isVisible = content.style.display !== 'none';
            content.style.display = isVisible ? 'none' : 'block';
            
            if (!isVisible) {
                content.classList.add('animate-slideDown');
            }
        }
    }

    // معالجة تغيير الخيارات
    handleOptionChange(e) {
        const element = e.target;
        const optionKey = element.dataset.option;
        
        if (optionKey && element.value !== undefined) {
            settingsManager.updateDefaultValue(optionKey, element.value);
        }
    }

    // معالجة تبديل الخيارات
    handleOptionToggle(e) {
        if (e.target.classList.contains('option-toggle')) {
            const optionKey = e.target.closest('[data-option]')?.dataset.option;
            if (optionKey) {
                this.toggleOption(optionKey);
            }
        }
    }

    // معالجة التوليد
    async handleGenerate() {
        const userInput = this.elements.mainPrompt?.value?.trim();

        if (!userInput) {
            this.showAlert('يرجى إدخال وصف للمشروع أولاً', 'warning');
            this.elements.mainPrompt?.focus();
            return;
        }

        // التحقق من طول النص
        if (userInput.length < 10) {
            this.showAlert('يرجى إدخال وصف أكثر تفصيلاً (على الأقل 10 أحرف)', 'warning');
            return;
        }

        try {
            this.showLoading(true);
            this.updateGenerateButton(false);
            this.resetProgress();

            const result = await promptGenerator.generateEnhancedPrompt(
                userInput,
                (progress) => this.updateProgress(progress)
            );

            if (result && !promptGenerator.isPaused) {
                this.displayResult(result);
                this.showAlert('تم توليد البرومبت بنجاح!', 'success');

                // حفظ في التاريخ
                promptGenerator.saveGeneratedPrompt();

                // إضافة إحصائيات
                this.updateGenerationStats();
            } else if (promptGenerator.isPaused) {
                this.showAlert('تم إيقاف العملية مؤقتاً', 'warning');
            }

        } catch (error) {
            console.error('خطأ في التوليد:', error);

            // تحديد نوع الخطأ وإظهار رسالة مناسبة
            let errorMessage = 'حدث خطأ غير متوقع';

            if (error.message.includes('API')) {
                errorMessage = 'خطأ في الاتصال بخدمة الذكاء الصناعي. يرجى المحاولة مرة أخرى.';
            } else if (error.message.includes('network') || error.message.includes('fetch')) {
                errorMessage = 'خطأ في الاتصال بالإنترنت. يرجى التحقق من الاتصال والمحاولة مرة أخرى.';
            } else if (error.message.includes('timeout')) {
                errorMessage = 'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.';
            }

            this.showAlert(errorMessage, 'error');
        } finally {
            this.showLoading(false);
            this.updateGenerateButton(true);
            this.isPaused = false;
        }
    }

    // معالجة التصدير
    async handleExport(format) {
        const content = promptGenerator.generatedPrompt;
        
        if (!content) {
            this.showAlert('لا يوجد محتوى للتصدير. يرجى توليد برومبت أولاً.', 'warning');
            return;
        }
        
        try {
            const filename = `البرومبت_المولد_${new Date().toISOString().split('T')[0]}`;
            
            switch (format) {
                case 'html':
                    await exportManager.exportToHTML(content, filename);
                    break;
                case 'excel':
                    await exportManager.exportToExcel(content, filename);
                    break;
                case 'pdf':
                    await exportManager.exportToPDF(content, filename);
                    break;
                case 'txt':
                    exportManager.exportToTXT(content, filename);
                    break;
                case 'zip':
                    await exportManager.exportToZIP(content, filename);
                    break;
            }
            
            this.showAlert(`تم تصدير الملف بصيغة ${format.toUpperCase()} بنجاح!`, 'success');
            
        } catch (error) {
            console.error('خطأ في التصدير:', error);
            this.showAlert(`فشل في التصدير: ${error.message}`, 'error');
        }
    }

    // معالجة استيراد الإعدادات
    handleImportSettings() {
        this.elements.settingsFileInput?.click();
    }

    // معالجة تصدير الإعدادات
    handleExportSettings() {
        try {
            const settings = settingsManager.exportSettings();
            exportManager.exportSettings(settings);
            this.showAlert('تم تصدير الإعدادات بنجاح!', 'success');
        } catch (error) {
            console.error('خطأ في تصدير الإعدادات:', error);
            this.showAlert(`فشل في تصدير الإعدادات: ${error.message}`, 'error');
        }
    }

    // معالجة اختيار ملف الإعدادات
    async handleSettingsFileSelect(e) {
        const file = e.target.files[0];
        if (!file) return;
        
        try {
            const settings = await exportManager.importSettings(file);
            await settingsManager.importSettings(settings);
            
            // إعادة تحميل الخيارات
            this.loadPromptOptions();
            this.applyUserPreferences();
            
            this.showAlert('تم استيراد الإعدادات بنجاح!', 'success');
            
        } catch (error) {
            console.error('خطأ في استيراد الإعدادات:', error);
            this.showAlert(`فشل في استيراد الإعدادات: ${error.message}`, 'error');
        }
        
        // إعادة تعيين قيمة الحقل
        e.target.value = '';
    }

    // معالجة الإيقاف المؤقت/الاستئناف
    handlePauseResume() {
        if (this.isPaused) {
            promptGenerator.resumeGeneration();
            this.isPaused = false;
            this.elements.pauseBtn.innerHTML = '<i class="fas fa-pause"></i> إيقاف مؤقت';
            this.elements.pauseBtn.classList.remove('btn-success');
            this.elements.pauseBtn.classList.add('btn-warning');
        } else {
            promptGenerator.pauseGeneration();
            this.isPaused = true;
            this.elements.pauseBtn.innerHTML = '<i class="fas fa-play"></i> استئناف';
            this.elements.pauseBtn.classList.remove('btn-warning');
            this.elements.pauseBtn.classList.add('btn-success');
        }
    }

    // إظهار/إخفاء شاشة التحميل
    showLoading(show) {
        if (this.elements.loadingOverlay) {
            if (show) {
                this.elements.loadingOverlay.classList.remove('d-none');
                this.elements.loadingOverlay.classList.add('animate-fadeIn');
            } else {
                this.elements.loadingOverlay.classList.add('animate-fadeOut');
                setTimeout(() => {
                    this.elements.loadingOverlay.classList.add('d-none');
                    this.elements.loadingOverlay.classList.remove('animate-fadeIn', 'animate-fadeOut');
                }, 300);
            }
        }
    }

    // تحديث مؤشر التقدم
    updateProgress(progress) {
        if (this.elements.progressBar) {
            this.elements.progressBar.style.width = `${progress.percentage}%`;
            this.elements.progressBar.setAttribute('aria-valuenow', progress.percentage);
        }
        
        if (this.elements.progressText) {
            this.elements.progressText.textContent = progress.message;
        }
        
        this.currentProgress = progress.percentage;
    }

    // تحديث زر التوليد
    updateGenerateButton(enabled) {
        if (this.elements.generateBtn) {
            this.elements.generateBtn.disabled = !enabled;
            
            if (enabled) {
                this.elements.generateBtn.innerHTML = '<i class="fas fa-magic"></i> توليد البرومبت الاحترافي';
                this.elements.generateBtn.classList.remove('btn-secondary');
                this.elements.generateBtn.classList.add('btn-primary');
            } else {
                this.elements.generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التوليد...';
                this.elements.generateBtn.classList.remove('btn-primary');
                this.elements.generateBtn.classList.add('btn-secondary');
            }
        }
    }

    // عرض النتيجة
    displayResult(content) {
        if (this.elements.generatedPrompt) {
            this.elements.generatedPrompt.textContent = content;
        }
        
        if (this.elements.resultsSection) {
            this.elements.resultsSection.classList.remove('d-none');
            this.elements.resultsSection.classList.add('animate-slideUp');
            
            // التمرير إلى النتائج
            setTimeout(() => {
                this.elements.resultsSection.scrollIntoView({ 
                    behavior: 'smooth', 
                    block: 'start' 
                });
            }, 300);
        }
    }

    // إظهار تنبيه
    showAlert(message, type = 'info') {
        const alertClass = {
            success: 'alert-success',
            error: 'alert-danger',
            warning: 'alert-warning',
            info: 'alert-info'
        }[type] || 'alert-info';
        
        const alertHTML = `
            <div class="alert ${alertClass} alert-dismissible fade show animate-slideDown" role="alert">
                <i class="fas fa-${this.getAlertIcon(type)}"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // إضافة التنبيه إلى أعلى الصفحة
        const container = document.querySelector('.container');
        if (container) {
            const alertDiv = document.createElement('div');
            alertDiv.innerHTML = alertHTML;
            container.insertBefore(alertDiv.firstElementChild, container.firstChild);
            
            // إزالة التنبيه تلقائياً بعد 5 ثوان
            setTimeout(() => {
                const alert = container.querySelector('.alert');
                if (alert) {
                    alert.classList.add('animate-fadeOut');
                    setTimeout(() => alert.remove(), 300);
                }
            }, 5000);
        }
    }

    // الحصول على أيقونة التنبيه
    getAlertIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-triangle',
            warning: 'exclamation-circle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    // إضافة حركات للخيارات
    addOptionAnimations() {
        const options = document.querySelectorAll('.option-group');
        options.forEach((option, index) => {
            option.style.animationDelay = `${index * 0.1}s`;
        });
    }

    // تطبيق تفضيلات المستخدم
    applyUserPreferences() {
        settingsManager.applySettingsToUI();
    }

    // تحديث الواجهة عند تغيير الحجم
    handleResize() {
        // تحديث تخطيط الواجهة حسب حجم الشاشة
        const isMobile = window.innerWidth < 768;
        document.body.classList.toggle('mobile-view', isMobile);
    }

    // إعادة تعيين مؤشر التقدم
    resetProgress() {
        this.currentProgress = 0;
        if (this.elements.progressBar) {
            this.elements.progressBar.style.width = '0%';
            this.elements.progressBar.setAttribute('aria-valuenow', 0);
        }
        if (this.elements.progressText) {
            this.elements.progressText.textContent = 'بدء العملية...';
        }
    }

    // تحديث إحصائيات التوليد
    updateGenerationStats() {
        try {
            const stats = JSON.parse(localStorage.getItem('generation_stats') || '{}');
            stats.totalGenerations = (stats.totalGenerations || 0) + 1;
            stats.lastGeneration = new Date().toISOString();
            localStorage.setItem('generation_stats', JSON.stringify(stats));
        } catch (error) {
            console.warn('فشل في تحديث الإحصائيات:', error);
        }
    }

    // إظهار نصائح للمستخدم
    showTips() {
        const tips = [
            'استخدم وصفاً مفصلاً لمشروعك للحصول على أفضل النتائج',
            'يمكنك تعديل الخيارات حسب احتياجاتك قبل التوليد',
            'استخدم خيارات التصدير المختلفة لحفظ النتائج',
            'يمكنك حفظ إعداداتك واستيرادها لاحقاً',
            'استخدم زر الإيقاف المؤقت إذا كنت تريد إيقاف العملية'
        ];

        const randomTip = tips[Math.floor(Math.random() * tips.length)];
        this.showAlert(`💡 نصيحة: ${randomTip}`, 'info');
    }

    // التحقق من حالة الاتصال
    checkConnectionStatus() {
        if (!navigator.onLine) {
            this.showAlert('⚠️ لا يوجد اتصال بالإنترنت. بعض الميزات قد لا تعمل.', 'warning');
            return false;
        }
        return true;
    }

    // حفظ تلقائي للمحتوى
    autoSave() {
        const content = this.elements.mainPrompt?.value;
        if (content && content.trim()) {
            localStorage.setItem('auto_saved_content', content);
        }
    }

    // استعادة المحتوى المحفوظ تلقائياً
    restoreAutoSaved() {
        const saved = localStorage.getItem('auto_saved_content');
        if (saved && this.elements.mainPrompt && !this.elements.mainPrompt.value.trim()) {
            this.elements.mainPrompt.value = saved;
            this.showAlert('تم استعادة المحتوى المحفوظ تلقائياً', 'info');
        }
    }

    // تنظيف البيانات المؤقتة
    cleanupTempData() {
        // إزالة البيانات المؤقتة القديمة
        const tempKeys = ['auto_saved_content'];
        tempKeys.forEach(key => {
            const data = localStorage.getItem(key);
            if (data) {
                try {
                    const timestamp = localStorage.getItem(key + '_timestamp');
                    if (timestamp) {
                        const age = Date.now() - parseInt(timestamp);
                        if (age > 24 * 60 * 60 * 1000) { // أكثر من 24 ساعة
                            localStorage.removeItem(key);
                            localStorage.removeItem(key + '_timestamp');
                        }
                    }
                } catch (error) {
                    console.warn('خطأ في تنظيف البيانات المؤقتة:', error);
                }
            }
        });
    }
}

// إنشاء مثيل عام من مدير واجهة المستخدم
const uiManager = new UIManager();

// تهيئة الواجهة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    uiManager.init();
});

// معالجة تغيير حجم النافذة
window.addEventListener('resize', () => {
    uiManager.handleResize();
});

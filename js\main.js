// ===== الملف الرئيسي للتطبيق =====

class PromptProApp {
    constructor() {
        this.isInitialized = false;
        this.version = '1.0.0';
        this.buildDate = new Date().toISOString();
    }

    // تهيئة التطبيق
    async init() {
        if (this.isInitialized) return;

        try {
            console.log('🚀 بدء تهيئة مولد البرومبت الاحترافي...');
            
            // التحقق من دعم المتصفح
            this.checkBrowserSupport();
            
            // تحميل المكتبات المطلوبة
            await this.loadRequiredLibraries();
            
            // تهيئة المكونات
            this.initializeComponents();
            
            // تطبيق الإعدادات الأولية
            this.applyInitialSettings();
            
            // إعداد معالجات الأخطاء
            this.setupErrorHandlers();
            
            // تنظيف البيانات القديمة
            this.performMaintenance();
            
            this.isInitialized = true;
            console.log('✅ تم تهيئة التطبيق بنجاح');
            
            // إظهار رسالة ترحيب
            this.showWelcomeMessage();
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة التطبيق:', error);
            this.handleInitializationError(error);
        }
    }

    // التحقق من دعم المتصفح
    checkBrowserSupport() {
        const requiredFeatures = [
            'fetch',
            'Promise',
            'localStorage',
            'JSON',
            'FileReader',
            'Blob',
            'URL'
        ];

        const unsupportedFeatures = requiredFeatures.filter(feature => {
            return !(feature in window) && !(feature in window.constructor.prototype);
        });

        if (unsupportedFeatures.length > 0) {
            throw new Error(`المتصفح لا يدعم الميزات المطلوبة: ${unsupportedFeatures.join(', ')}`);
        }

        // التحقق من إصدار المتصفح
        const userAgent = navigator.userAgent;
        const isOldBrowser = /MSIE|Trident/.test(userAgent);
        
        if (isOldBrowser) {
            this.showBrowserWarning();
        }
    }

    // تحميل المكتبات المطلوبة
    async loadRequiredLibraries() {
        const libraries = [
            {
                name: 'jsPDF',
                url: 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js',
                check: () => window.jspdf
            },
            {
                name: 'SheetJS',
                url: 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js',
                check: () => window.XLSX
            },
            {
                name: 'JSZip',
                url: 'https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js',
                check: () => window.JSZip
            }
        ];

        for (const lib of libraries) {
            if (!lib.check()) {
                console.log(`📦 تحميل مكتبة ${lib.name}...`);
                await this.loadScript(lib.url);
                
                // التحقق من التحميل
                if (!lib.check()) {
                    console.warn(`⚠️ فشل في تحميل مكتبة ${lib.name}`);
                }
            }
        }
    }

    // تحميل سكريبت خارجي
    loadScript(url) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = url;
            script.onload = resolve;
            script.onerror = () => reject(new Error(`فشل في تحميل: ${url}`));
            document.head.appendChild(script);
        });
    }

    // تهيئة المكونات
    initializeComponents() {
        console.log('🔧 تهيئة المكونات...');
        
        // التحقق من وجود المكونات
        if (typeof settingsManager === 'undefined') {
            throw new Error('مدير الإعدادات غير متوفر');
        }
        
        if (typeof apiManager === 'undefined') {
            throw new Error('مدير API غير متوفر');
        }
        
        if (typeof promptGenerator === 'undefined') {
            throw new Error('مولد البرومبت غير متوفر');
        }
        
        if (typeof exportManager === 'undefined') {
            throw new Error('مدير التصدير غير متوفر');
        }
        
        if (typeof uiManager === 'undefined') {
            throw new Error('مدير واجهة المستخدم غير متوفر');
        }
        
        console.log('✅ جميع المكونات متوفرة');
    }

    // تطبيق الإعدادات الأولية
    applyInitialSettings() {
        console.log('⚙️ تطبيق الإعدادات الأولية...');
        
        // تطبيق إعدادات واجهة المستخدم
        settingsManager.applySettingsToUI();
        
        // تحديث خيارات مولد البرومبت
        promptGenerator.updateOptions(settingsManager.settings);
        
        // تطبيق تفضيلات المستخدم
        this.applyUserPreferences();
    }

    // تطبيق تفضيلات المستخدم
    applyUserPreferences() {
        const preferences = settingsManager.userPreferences;
        
        // تطبيق السمة
        if (preferences.theme === 'dark') {
            document.body.classList.add('dark-theme');
        }
        
        // تطبيق الحركات
        if (!preferences.enableAnimations) {
            document.body.classList.add('no-animations');
        }
        
        // تطبيق اللغة
        document.documentElement.lang = preferences.language;
        document.documentElement.dir = preferences.language === 'ar' ? 'rtl' : 'ltr';
    }

    // إعداد معالجات الأخطاء
    setupErrorHandlers() {
        // معالج الأخطاء العام
        window.addEventListener('error', (event) => {
            console.error('خطأ في التطبيق:', event.error);
            this.handleGlobalError(event.error);
        });

        // معالج الأخطاء غير المعالجة
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Promise مرفوض:', event.reason);
            this.handleGlobalError(event.reason);
        });

        // معالج أخطاء الشبكة
        window.addEventListener('offline', () => {
            this.handleNetworkError('offline');
        });

        window.addEventListener('online', () => {
            this.handleNetworkError('online');
        });
    }

    // معالجة الأخطاء العامة
    handleGlobalError(error) {
        const errorInfo = {
            message: error.message || 'خطأ غير معروف',
            stack: error.stack,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        // حفظ الخطأ في التخزين المحلي للتشخيص
        this.logError(errorInfo);

        // إظهار رسالة خطأ للمستخدم
        if (uiManager && uiManager.isInitialized) {
            uiManager.showAlert('حدث خطأ غير متوقع. يرجى إعادة تحميل الصفحة.', 'error');
        }
    }

    // معالجة أخطاء الشبكة
    handleNetworkError(status) {
        if (status === 'offline') {
            if (uiManager && uiManager.isInitialized) {
                uiManager.showAlert('انقطع الاتصال بالإنترنت. بعض الميزات قد لا تعمل.', 'warning');
            }
        } else if (status === 'online') {
            if (uiManager && uiManager.isInitialized) {
                uiManager.showAlert('تم استعادة الاتصال بالإنترنت.', 'success');
            }
        }
    }

    // تسجيل الأخطاء
    logError(errorInfo) {
        try {
            const errors = JSON.parse(localStorage.getItem('app_errors') || '[]');
            errors.unshift(errorInfo);
            
            // الاحتفاظ بآخر 50 خطأ فقط
            if (errors.length > 50) {
                errors.splice(50);
            }
            
            localStorage.setItem('app_errors', JSON.stringify(errors));
        } catch (e) {
            console.error('فشل في حفظ معلومات الخطأ:', e);
        }
    }

    // معالجة خطأ التهيئة
    handleInitializationError(error) {
        const errorMessage = `
            <div class="alert alert-danger" role="alert">
                <h4 class="alert-heading">خطأ في تهيئة التطبيق</h4>
                <p>${error.message}</p>
                <hr>
                <p class="mb-0">يرجى إعادة تحميل الصفحة أو الاتصال بالدعم الفني.</p>
                <button class="btn btn-outline-danger mt-2" onclick="location.reload()">
                    إعادة تحميل الصفحة
                </button>
            </div>
        `;
        
        document.body.innerHTML = errorMessage;
    }

    // إظهار تحذير المتصفح القديم
    showBrowserWarning() {
        const warning = `
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <strong>تحذير:</strong> أنت تستخدم متصفحاً قديماً. قد لا تعمل جميع الميزات بشكل صحيح.
                يُنصح بتحديث المتصفح للحصول على أفضل تجربة.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        document.body.insertAdjacentHTML('afterbegin', warning);
    }

    // إظهار رسالة ترحيب
    showWelcomeMessage() {
        if (this.isFirstVisit()) {
            setTimeout(() => {
                if (uiManager && uiManager.isInitialized) {
                    uiManager.showAlert('مرحباً بك في مولد البرومبت الاحترافي! ابدأ بإدخال وصف مشروعك.', 'info');
                }
            }, 1000);
        }
    }

    // التحقق من الزيارة الأولى
    isFirstVisit() {
        const hasVisited = localStorage.getItem('has_visited');
        if (!hasVisited) {
            localStorage.setItem('has_visited', 'true');
            return true;
        }
        return false;
    }

    // تنظيف البيانات القديمة
    performMaintenance() {
        console.log('🧹 تنظيف البيانات القديمة...');
        
        try {
            // تنظيف البيانات القديمة
            settingsManager.cleanupOldData();
            
            // تنظيف أخطاء قديمة
            this.cleanupOldErrors();
            
            console.log('✅ تم تنظيف البيانات بنجاح');
        } catch (error) {
            console.warn('⚠️ خطأ في تنظيف البيانات:', error);
        }
    }

    // تنظيف الأخطاء القديمة
    cleanupOldErrors() {
        try {
            const errors = JSON.parse(localStorage.getItem('app_errors') || '[]');
            const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
            
            const recentErrors = errors.filter(error => {
                const errorDate = new Date(error.timestamp).getTime();
                return errorDate > oneWeekAgo;
            });
            
            localStorage.setItem('app_errors', JSON.stringify(recentErrors));
        } catch (error) {
            console.warn('فشل في تنظيف الأخطاء القديمة:', error);
        }
    }

    // الحصول على معلومات التطبيق
    getAppInfo() {
        return {
            version: this.version,
            buildDate: this.buildDate,
            isInitialized: this.isInitialized,
            userAgent: navigator.userAgent,
            language: navigator.language,
            platform: navigator.platform,
            storageUsage: settingsManager ? settingsManager.calculateStorageUsage() : null,
            apiStats: apiManager ? apiManager.getUsageStats() : null
        };
    }

    // إعادة تشغيل التطبيق
    restart() {
        console.log('🔄 إعادة تشغيل التطبيق...');
        
        // إعادة تعيين الحالة
        this.isInitialized = false;
        
        // مسح المتغيرات العامة
        if (window.uiManager) {
            window.uiManager.isInitialized = false;
        }
        
        // إعادة تحميل الصفحة
        setTimeout(() => {
            location.reload();
        }, 500);
    }

    // تصدير بيانات التشخيص
    exportDiagnostics() {
        const diagnostics = {
            appInfo: this.getAppInfo(),
            errors: JSON.parse(localStorage.getItem('app_errors') || '[]'),
            settings: settingsManager ? settingsManager.settings : null,
            preferences: settingsManager ? settingsManager.userPreferences : null,
            timestamp: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(diagnostics, null, 2)], { 
            type: 'application/json' 
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `prompt_pro_diagnostics_${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

// إنشاء مثيل التطبيق
const app = new PromptProApp();

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    app.init();
});

// إضافة دوال مساعدة للوحة التحكم
window.PromptPro = {
    app: app,
    restart: () => app.restart(),
    info: () => app.getAppInfo(),
    diagnostics: () => app.exportDiagnostics(),
    clearData: () => {
        if (confirm('هل أنت متأكد من حذف جميع البيانات؟')) {
            localStorage.clear();
            location.reload();
        }
    }
};

console.log('🎉 مولد البرومبت الاحترافي جاهز للاستخدام!');
console.log('💡 استخدم PromptPro.info() للحصول على معلومات التطبيق');
console.log('🔧 استخدم PromptPro.diagnostics() لتصدير بيانات التشخيص');

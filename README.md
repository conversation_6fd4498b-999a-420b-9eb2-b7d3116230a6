# مولد البرومبت الاحترافي - Prompt Pro Generator

أداة احترافية متقدمة تستخدم الذكاء الصناعي لتوليد برومبت متكامل وشامل لجميع المجالات مع خيارات متقدمة للتخصيص والتصدير.

## 🌟 الميزات الرئيسية

### 🧠 توليد ذكي للبرومبت
- استخدام تقنية Gemini AI المتقدمة
- تدوير ذكي لمفاتيح API لضمان الاستمرارية
- تقسيم المحتوى الكبير تلقائياً
- معالجة متقدمة للنصوص الطويلة

### 🎯 خيارات شاملة ومتقدمة
- **السياق**: تحديد الخلفية والمجال
- **الهدف الرئيسي**: تحديد الأهداف بدقة
- **المجال المتخصص**: برمجة، تسويق، تصميم، محتوى، سيو
- **الجمهور المستهدف**: تحديد الفئة المستهدفة
- **المدخلات والمخرجات**: تحديد أنواع البيانات
- **التصميم والتنسيق**: متطلبات التصميم
- **الوظائف المحددة**: تفصيل الوظائف المطلوبة
- **دمج الذكاء الصناعي**: إعدادات AI متقدمة
- **خيارات متقدمة**: تحكم شامل في الأداة
- **التحقق والتحسين**: ضمان الجودة

### 📊 واجهة مستخدم احترافية
- تصميم عصري متجاوب (RTL)
- خطوط عربية احترافية (Cairo, Tajawal)
- حركات وتأثيرات سلسة
- مؤشر تقدم تفاعلي
- إمكانية الإيقاف المؤقت والاستئناف

### 📤 تصدير متعدد الصيغ
- **HTML**: مع محرك بحث تفاعلي
- **PDF**: بدعم اللغة العربية
- **Excel**: جداول منظمة
- **TXT**: نص خام
- **ZIP**: أرشيف شامل

### ⚙️ إدارة الإعدادات
- حفظ واستيراد الإعدادات
- نسخ احتياطية تلقائية
- تخصيص كامل للخيارات
- تاريخ البرومبت المولدة

## 🚀 التشغيل السريع

### متطلبات النظام
- متصفح حديث يدعم ES6+
- اتصال بالإنترنت لـ APIs
- JavaScript مفعل

### التشغيل
1. افتح ملف `index.html` في المتصفح
2. أدخل وصف مشروعك في الحقل الرئيسي
3. اختر الخيارات المناسبة أو اتركها افتراضية
4. اضغط "توليد البرومبت الاحترافي"
5. انتظر حتى اكتمال العملية
6. صدّر النتيجة بالصيغة المطلوبة

## 📁 هيكل المشروع

```
Prompt Pro/
├── index.html              # الصفحة الرئيسية
├── css/                    # ملفات الأنماط
│   ├── styles.css          # الأنماط الأساسية
│   ├── components.css      # أنماط المكونات
│   └── animations.css      # الحركات والتأثيرات
├── js/                     # ملفات JavaScript
│   ├── config.js           # إعدادات التطبيق
│   ├── api-manager.js      # إدارة APIs
│   ├── prompt-generator.js # محرك توليد البرومبت
│   ├── export-manager.js   # إدارة التصدير
│   ├── settings-manager.js # إدارة الإعدادات
│   ├── ui-manager.js       # إدارة واجهة المستخدم
│   └── main.js             # الملف الرئيسي
└── README.md               # هذا الملف
```

## 🔧 التكوين المتقدم

### إعداد مفاتيح API
المفاتيح محددة مسبقاً في `js/config.js`. يمكن تعديلها حسب الحاجة:

```javascript
GEMINI_API_KEYS: [
    'your-api-key-1',
    'your-api-key-2',
    // المزيد من المفاتيح...
]
```

### تخصيص الخيارات
يمكن تعديل الخيارات الافتراضية في `CONFIG.DEFAULT_PROMPT_OPTIONS`:

```javascript
context: {
    enabled: true,
    title: '🧠 السياق',
    description: 'تحديد السياق والخلفية للمشروع',
    defaultValue: 'النص الافتراضي...'
}
```

## 🎨 التخصيص

### الألوان والتصميم
- متغيرات CSS في `:root` بملف `styles.css`
- دعم كامل للـ RTL
- تصميم متجاوب لجميع الأجهزة

### الخطوط
- Cairo: للعناوين والنصوص المهمة
- Tajawal: للنصوص العادية
- دعم كامل للعربية

## 🔍 استكشاف الأخطاء

### مشاكل شائعة
1. **خطأ في API**: تحقق من مفاتيح API
2. **بطء في التحميل**: تحقق من الاتصال بالإنترنت
3. **مشاكل التصدير**: تأكد من دعم المتصفح للمكتبات

### أدوات التشخيص
```javascript
// في وحدة التحكم
PromptPro.info()        // معلومات التطبيق
PromptPro.diagnostics() // تصدير بيانات التشخيص
PromptPro.restart()     // إعادة تشغيل التطبيق
```

## 📈 الأداء والتحسين

### إدارة الذاكرة
- تنظيف تلقائي للبيانات القديمة
- حد أقصى 50 برومبت في التاريخ
- ضغط البيانات المحفوظة

### تحسين API
- تدوير ذكي للمفاتيح
- إعادة المحاولة التلقائية
- تقسيم المحتوى الكبير
- إدارة حدود التوكن

## 🛡️ الأمان والخصوصية

- جميع البيانات محفوظة محلياً
- لا يتم إرسال بيانات شخصية
- مفاتيح API محمية
- تشفير البيانات الحساسة

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. Fork المشروع
2. إنشاء branch جديد
3. تطبيق التغييرات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## 📞 الدعم

للدعم الفني أو الاستفسارات:
- إنشاء Issue في GitHub
- مراجعة الوثائق
- استخدام أدوات التشخيص المدمجة

## 🔄 التحديثات

### الإصدار 1.0.0
- إطلاق أولي
- جميع الميزات الأساسية
- دعم كامل للعربية
- تصدير متعدد الصيغ

---

**مولد البرومبت الاحترافي** - أداة متقدمة لتوليد برومبت احترافي باستخدام الذكاء الصناعي 🚀

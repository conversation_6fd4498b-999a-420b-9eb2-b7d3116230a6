<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد البرومبت الاحترافي - أداة ذكية لتوليد برومبت متكامل</title>
    <meta name="description" content="أداة احترافية تستخدم الذكاء الصناعي لتوليد برومبت متكامل وشامل لجميع المجالات مع خيارات متقدمة للتخصيص والتصدير">
    <meta name="keywords" content="مولد برومبت, ذكاء صناعي, أداة احترافية, توليد محتوى, برمجة, تسويق رقمي">
    <meta name="author" content="Prompt Pro Generator">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="مولد البرومبت الاحترافي - أداة ذكية لتوليد برومبت متكامل">
    <meta property="og:description" content="أداة احترافية تستخدم الذكاء الصناعي لتوليد برومبت متكامل وشامل لجميع المجالات">
    <meta property="og:type" content="website">
    <meta property="og:locale" content="ar_AR">
    
    <!-- Schema.org Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "مولد البرومبت الاحترافي",
        "description": "أداة احترافية تستخدم الذكاء الصناعي لتوليد برومبت متكامل وشامل لجميع المجالات",
        "applicationCategory": "ProductivityApplication",
        "operatingSystem": "Web Browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "creator": {
            "@type": "Organization",
            "name": "Prompt Pro",
            "expertise": "Artificial Intelligence, Content Generation"
        }
    }
    </script>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/animations.css">
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay d-none">
        <div class="loading-content">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <h4 class="mt-3">جاري معالجة طلبك...</h4>
            <div class="progress mt-3" style="width: 300px;">
                <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                     role="progressbar" style="width: 0%"></div>
            </div>
            <p id="progressText" class="mt-2">بدء العملية...</p>
            <button id="pauseBtn" class="btn btn-warning mt-2">
                <i class="fas fa-pause"></i> إيقاف مؤقت
            </button>
        </div>
    </div>

    <!-- Header -->
    <header class="header-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="main-title">
                        <i class="fas fa-brain text-primary"></i>
                        مولد البرومبت الاحترافي
                    </h1>
                    <p class="subtitle">أداة ذكية تستخدم الذكاء الصناعي لتوليد برومبت متكامل واحترافي</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="header-actions">
                        <button class="btn btn-outline-primary" id="importSettings">
                            <i class="fas fa-upload"></i> استيراد إعدادات
                        </button>
                        <button class="btn btn-outline-secondary" id="exportSettings">
                            <i class="fas fa-download"></i> تصدير إعدادات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Input Section -->
            <section class="input-section card shadow-lg mb-4">
                <div class="card-header bg-primary text-white">
                    <h3><i class="fas fa-edit"></i> إدخال المتطلبات الأساسية</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <label for="mainPrompt" class="form-label">وصف المجال أو المشروع المطلوب:</label>
                            <textarea id="mainPrompt" class="form-control" rows="4" 
                                placeholder="اكتب هنا وصفاً مفصلاً للمجال أو المشروع الذي تريد إنشاء برومبت احترافي له..."></textarea>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Options Section -->
            <section class="options-section card shadow-lg mb-4">
                <div class="card-header bg-success text-white">
                    <h3><i class="fas fa-cogs"></i> خيارات البرومبت المتقدمة</h3>
                </div>
                <div class="card-body">
                    <div id="promptOptions">
                        <!-- Options will be loaded here dynamically -->
                    </div>
                </div>
            </section>

            <!-- Generate Button -->
            <div class="text-center mb-4">
                <button id="generateBtn" class="btn btn-primary btn-lg px-5">
                    <i class="fas fa-magic"></i> توليد البرومبت الاحترافي
                </button>
            </div>

            <!-- Results Section -->
            <section id="resultsSection" class="results-section card shadow-lg mb-4 d-none">
                <div class="card-header bg-info text-white">
                    <h3><i class="fas fa-file-alt"></i> النتائج المولدة</h3>
                </div>
                <div class="card-body">
                    <div id="generatedPrompt" class="generated-content"></div>
                    
                    <!-- Export Options -->
                    <div class="export-options mt-4">
                        <h5><i class="fas fa-download"></i> خيارات التصدير:</h5>
                        <div class="btn-group" role="group">
                            <button class="btn btn-outline-primary" id="exportHTML">
                                <i class="fas fa-code"></i> HTML
                            </button>
                            <button class="btn btn-outline-success" id="exportExcel">
                                <i class="fas fa-file-excel"></i> Excel
                            </button>
                            <button class="btn btn-outline-danger" id="exportPDF">
                                <i class="fas fa-file-pdf"></i> PDF
                            </button>
                            <button class="btn btn-outline-secondary" id="exportTXT">
                                <i class="fas fa-file-alt"></i> TXT
                            </button>
                            <button class="btn btn-outline-warning" id="exportZIP">
                                <i class="fas fa-file-archive"></i> ZIP
                            </button>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer-section">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; 2024 مولد البرومبت الاحترافي. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-end">
                    <p>مدعوم بتقنية الذكاء الصناعي المتقدمة</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Hidden File Input -->
    <input type="file" id="settingsFileInput" accept=".json" style="display: none;">

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="js/config.js"></script>
    <script src="js/api-manager.js"></script>
    <script src="js/prompt-generator.js"></script>
    <script src="js/export-manager.js"></script>
    <script src="js/settings-manager.js"></script>
    <script src="js/ui-manager.js"></script>
    <script src="js/main.js"></script>
</body>
</html>

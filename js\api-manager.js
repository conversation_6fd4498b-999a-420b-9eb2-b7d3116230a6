// ===== مدير APIs والتدوير الذكي =====

class APIManager {
    constructor() {
        this.apiKeys = CONFIG.GEMINI_API_KEYS;
        this.currentKeyIndex = 0;
        this.apiUsage = this.loadAPIUsage();
        this.requestQueue = [];
        this.isProcessing = false;
        this.retryAttempts = CONFIG.API_SETTINGS.RETRY_ATTEMPTS;
        this.retryDelay = CONFIG.API_SETTINGS.RETRY_DELAY;
    }

    // تحميل إحصائيات استخدام API من التخزين المحلي
    loadAPIUsage() {
        const stored = localStorage.getItem(CONFIG.STORAGE_KEYS.API_USAGE);
        if (stored) {
            return JSON.parse(stored);
        }
        
        // إنشاء إحصائيات جديدة لكل مفتاح
        const usage = {};
        this.apiKeys.forEach((key, index) => {
            usage[index] = {
                requests: 0,
                errors: 0,
                lastUsed: null,
                isBlocked: false,
                blockUntil: null
            };
        });
        return usage;
    }

    // حفظ إحصائيات الاستخدام
    saveAPIUsage() {
        localStorage.setItem(CONFIG.STORAGE_KEYS.API_USAGE, JSON.stringify(this.apiUsage));
    }

    // الحصول على أفضل مفتاح API متاح
    getBestAvailableKey() {
        const now = Date.now();
        
        // فحص المفاتيح المحظورة وإلغاء الحظر إذا انتهت المدة
        Object.keys(this.apiUsage).forEach(keyIndex => {
            const usage = this.apiUsage[keyIndex];
            if (usage.isBlocked && usage.blockUntil && now > usage.blockUntil) {
                usage.isBlocked = false;
                usage.blockUntil = null;
            }
        });

        // البحث عن أفضل مفتاح متاح
        let bestKey = null;
        let bestScore = -1;

        this.apiKeys.forEach((key, index) => {
            const usage = this.apiUsage[index];
            
            if (!usage.isBlocked) {
                // حساب نقاط الجودة (أقل استخدام + أقل أخطاء = أفضل)
                const score = 1000 - usage.requests - (usage.errors * 10);
                
                if (score > bestScore) {
                    bestScore = score;
                    bestKey = index;
                }
            }
        });

        return bestKey !== null ? bestKey : 0; // العودة للمفتاح الأول كاحتياطي
    }

    // تحديث إحصائيات المفتاح
    updateKeyUsage(keyIndex, success = true) {
        const usage = this.apiUsage[keyIndex];
        usage.requests++;
        usage.lastUsed = Date.now();
        
        if (!success) {
            usage.errors++;
            
            // حظر المفتاح مؤقتاً إذا تجاوز عدد الأخطاء الحد المسموح
            if (usage.errors >= 5) {
                usage.isBlocked = true;
                usage.blockUntil = Date.now() + (30 * 60 * 1000); // 30 دقيقة
            }
        }
        
        this.saveAPIUsage();
    }

    // إرسال طلب إلى Gemini API
    async sendRequest(prompt, options = {}) {
        const keyIndex = this.getBestAvailableKey();
        const apiKey = this.apiKeys[keyIndex];
        
        const requestData = {
            contents: [{
                parts: [{ text: prompt }]
            }],
            generationConfig: {
                temperature: options.temperature || CONFIG.API_SETTINGS.TEMPERATURE,
                topP: options.topP || CONFIG.API_SETTINGS.TOP_P,
                topK: options.topK || CONFIG.API_SETTINGS.TOP_K,
                maxOutputTokens: options.maxTokens || CONFIG.API_SETTINGS.MAX_TOKENS
            }
        };

        try {
            const response = await this.makeAPICall(apiKey, requestData);
            this.updateKeyUsage(keyIndex, true);
            return this.parseResponse(response);
        } catch (error) {
            this.updateKeyUsage(keyIndex, false);
            throw error;
        }
    }

    // تنفيذ استدعاء API مع إعادة المحاولة
    async makeAPICall(apiKey, requestData, attempt = 1) {
        const url = `${CONFIG.API_SETTINGS.BASE_URL}?key=${apiKey}`;
        
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData),
                signal: AbortSignal.timeout(CONFIG.API_SETTINGS.TIMEOUT)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`API Error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
            }

            return await response.json();
        } catch (error) {
            if (attempt < this.retryAttempts) {
                await this.delay(this.retryDelay * attempt);
                return this.makeAPICall(apiKey, requestData, attempt + 1);
            }
            throw error;
        }
    }

    // تحليل استجابة API
    parseResponse(response) {
        if (!response.candidates || response.candidates.length === 0) {
            throw new Error('لم يتم الحصول على استجابة صالحة من API');
        }

        const candidate = response.candidates[0];
        if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
            throw new Error('استجابة فارغة من API');
        }

        return candidate.content.parts[0].text;
    }

    // تقسيم المحتوى الكبير
    splitContent(content) {
        const maxSize = CONFIG.CONTENT_SPLITTING.MAX_CHUNK_SIZE;
        const overlapSize = CONFIG.CONTENT_SPLITTING.OVERLAP_SIZE;
        const minSize = CONFIG.CONTENT_SPLITTING.MIN_CHUNK_SIZE;
        
        if (content.length <= maxSize) {
            return [content];
        }

        const chunks = [];
        let start = 0;

        while (start < content.length) {
            let end = Math.min(start + maxSize, content.length);
            
            // البحث عن نقطة قطع مناسبة (نهاية جملة أو فقرة)
            if (end < content.length) {
                const lastPeriod = content.lastIndexOf('.', end);
                const lastNewline = content.lastIndexOf('\n', end);
                const cutPoint = Math.max(lastPeriod, lastNewline);
                
                if (cutPoint > start + minSize) {
                    end = cutPoint + 1;
                }
            }

            chunks.push(content.substring(start, end));
            start = Math.max(end - overlapSize, start + minSize);
        }

        return chunks;
    }

    // معالجة المحتوى الكبير بالتقسيم
    async processLargeContent(content, promptTemplate, progressCallback) {
        const chunks = this.splitContent(content);
        const results = [];
        
        for (let i = 0; i < chunks.length; i++) {
            if (progressCallback) {
                progressCallback({
                    current: i + 1,
                    total: chunks.length,
                    message: `معالجة الجزء ${i + 1} من ${chunks.length}`
                });
            }

            const chunkPrompt = promptTemplate.replace('[CONTENT]', chunks[i]);
            
            try {
                const result = await this.sendRequest(chunkPrompt);
                results.push(result);
                
                // تأخير قصير بين الطلبات لتجنب تجاوز الحدود
                if (i < chunks.length - 1) {
                    await this.delay(1000);
                }
            } catch (error) {
                console.error(`خطأ في معالجة الجزء ${i + 1}:`, error);
                results.push(`خطأ في معالجة الجزء ${i + 1}: ${error.message}`);
            }
        }

        return results;
    }

    // دالة التأخير
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // إضافة طلب إلى الطابور
    addToQueue(request) {
        this.requestQueue.push(request);
        this.processQueue();
    }

    // معالجة طابور الطلبات
    async processQueue() {
        if (this.isProcessing || this.requestQueue.length === 0) {
            return;
        }

        this.isProcessing = true;

        while (this.requestQueue.length > 0) {
            const request = this.requestQueue.shift();
            
            try {
                const result = await this.sendRequest(request.prompt, request.options);
                request.resolve(result);
            } catch (error) {
                request.reject(error);
            }

            // تأخير بين الطلبات
            await this.delay(500);
        }

        this.isProcessing = false;
    }

    // إحصائيات الاستخدام
    getUsageStats() {
        const stats = {
            totalRequests: 0,
            totalErrors: 0,
            activeKeys: 0,
            blockedKeys: 0
        };

        Object.values(this.apiUsage).forEach(usage => {
            stats.totalRequests += usage.requests;
            stats.totalErrors += usage.errors;
            
            if (usage.isBlocked) {
                stats.blockedKeys++;
            } else {
                stats.activeKeys++;
            }
        });

        return stats;
    }

    // إعادة تعيين إحصائيات مفتاح معين
    resetKeyStats(keyIndex) {
        if (this.apiUsage[keyIndex]) {
            this.apiUsage[keyIndex] = {
                requests: 0,
                errors: 0,
                lastUsed: null,
                isBlocked: false,
                blockUntil: null
            };
            this.saveAPIUsage();
        }
    }

    // إعادة تعيين جميع الإحصائيات
    resetAllStats() {
        this.apiKeys.forEach((_, index) => {
            this.resetKeyStats(index);
        });
    }
}

// إنشاء مثيل عام من مدير API
const apiManager = new APIManager();
